# Basket Command Implementation

## Overview
The `/basket` command has been successfully implemented in the PrismaCore plugin following the project's existing patterns and architecture.

## Command Functionality
- **Command**: `/basket <nick> <context...>`
- **Permission**: `basket.use`
- **Description**: Sends a formatted message to help resume purchases

## How it Works
1. Takes a player nickname and a context message as arguments
2. Randomly selects a name from the `BasketName` configuration list
3. Formats the message as: `&7(De <random_name>&7) <context>`
4. Sends the formatted message as a private message to the target player

## Configuration
Added to `config.yml`:
```yaml
# Basket Command Configuration
BasketName:
  - 6k2
  - yMiller
  - Aleexks
  - izLoki
```

## Files Modified/Created

### New Files:
- `paper/src/main/java/org/contrum/prisma/commands/user/BasketCommand.java`

### Modified Files:
- `paper/src/main/resources/config.yml` - Added BasketName configuration
- `paper/src/main/resources/lang_en.yml` - Added error messages
- `paper/src/main/resources/plugin.yml` - Added command registration
- `paper/src/main/java/org/contrum/prisma/PaperServices.java` - Added command registration

## Usage Examples

### From Console (Tebex):
```
basket yMiller ¿Necesitas ayuda para finalizar tu compra?
```

### Result for yMiller:
```
§7(De 6k2§7) ¿Necesitas ayuda para finalizar tu compra?
```
*(Note: "6k2" is randomly selected from the BasketName list)*

### From In-Game:
```
/basket PlayerName Has productos en tu carrito, ¿te ayudo?
```

## Features Implemented

### ✅ Core Requirements:
- [x] Command accepts `<nick>` and `<context...>` parameters
- [x] Random selection from `BasketName` configuration list
- [x] Proper message formatting with `&7(De <random_name>&7) <context>`
- [x] Sends private message to target player
- [x] No persistence of nick or context (only reads as arguments)

### ✅ Technical Requirements:
- [x] Uses ACF (Aikar's Command Framework) following project patterns
- [x] Proper permission system (`basket.use`)
- [x] Console and Tebex compatibility
- [x] Uses project's existing color translation (`CCP.translate`)
- [x] Follows project's error handling patterns
- [x] Registered in both ACF and plugin.yml

### ✅ Error Handling:
- [x] Player not found/offline handling
- [x] Empty BasketName configuration handling
- [x] Missing context parameter validation
- [x] Message length limiting (256 chars) to prevent spam
- [x] Proper error messages using project's translator system

### ✅ Integration:
- [x] Follows project's package structure (`commands.user`)
- [x] Uses project's dependency injection pattern
- [x] Compatible with existing command registration system
- [x] Uses project's configuration system
- [x] Uses project's internationalization system

## Testing Scenarios

### Valid Usage:
1. **Console**: `basket TestPlayer Hello, need help with your purchase?`
2. **In-game**: `/basket TestPlayer Your cart is waiting!`

### Error Cases:
1. **Player offline**: Shows "Player not found or offline!" message
2. **No context**: Shows usage message
3. **Empty config**: Shows "Basket command is disabled - no names configured!" message
4. **Long message**: Shows "Message too long! Maximum 256 characters." message

## Compatibility
- ✅ **Tebex**: Command works from console with placeholders
- ✅ **Console**: Full functionality available
- ✅ **In-game**: Full functionality with permission check
- ✅ **Multi-server**: Uses existing project infrastructure

## Security Considerations
- Permission-based access control
- Message length limiting
- Input validation for all parameters
- Safe random selection with bounds checking

## Performance
- Minimal overhead using `ThreadLocalRandom`
- Efficient string operations
- No database queries or heavy operations
- Follows project's existing patterns for optimal performance
