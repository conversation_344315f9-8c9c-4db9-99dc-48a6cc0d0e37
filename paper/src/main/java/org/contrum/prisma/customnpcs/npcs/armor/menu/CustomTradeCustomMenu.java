package org.contrum.prisma.customnpcs.npcs.armor.menu;

import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.Menu;
import org.contrum.chorpu.menu.storage.StorageMenu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.customnpcs.npcs.armor.menu.geyser.CustomTradeGeyserMenu;
import org.contrum.prisma.events.PlayerUpgradeArmorEvent;
import org.contrum.prisma.systems.impl.battlepass.BattlePassSystem;
import org.contrum.prisma.systems.impl.battlepass.prestige.BattlePassPrestigeManager;
import org.contrum.prisma.utils.InventoryUtils;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.menus.CoreMenu;
import org.contrum.prisma.utils.menus.geyser.CoreGeyserMenu;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;
import java.util.Map;

public class CustomTradeCustomMenu extends CoreMenu {
    private final PaperServices services;
    private final CustomArmorService customArmorService;
    private final Translator translator;

    private final CustomArmor requiredArmor;
    private final CustomArmor armor;

    private int compressCost;

    public CustomTradeCustomMenu(PaperServices services, CustomArmor armor) {
        super(services);
        this.services = services;
        this.customArmorService = services.getCustomArmorService();
        this.translator = services.getTranslator();
        this.armor = armor;
        this.requiredArmor = services.getCustomArmorService().getPreviousArmor(armor);

        this.compressCost = armor.getCompressRequired();
    }

    @Override
    public void open(Player player) {
        BattlePassSystem system = services.getSystemService().getSystem(BattlePassSystem.class);
        long newCost = system.getPrestigeManager().applyPrestigePriceModifier(player, compressCost, BattlePassPrestigeManager.DiscountType.ARMOR);
        if (compressCost != newCost)
            this.compressCost = (int) newCost;
        super.open(player);
    }

    @Override
    public int getRows(Player player) {
        return 4;
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {
        Map<Integer, Button> buttons = new HashMap<>();

        //Compress item
        ItemBuilder1_20 compressItem = new ItemBuilder1_20(customArmorService.getArmorCompress(armor, compressCost));
        compressItem.name(translator.getAsText(player, "CUSTOM_ARMOR.TRADE_MENU.COMPRESS_NAME", armor));
        buttons.put(4, Button.of(compressItem.build()));

        //Armor set
        int i = 19;
        for (ItemStack armorItem : customArmorService.getSet(armor)) {

            armorItem.setDisplayName(armorItem.getDisplayName() + CC.translate(" &7(Click para intercambiar)"));

            buttons.put(i, Button.of(armorItem, (c) -> {
                this.performTrade(c, customArmorService.updateItem(armorItem));
            }));

            i++;
            //Blank space
            if (i == 23) i++;
        }

        return buttons;
    }

    @Override
    public boolean shouldCancel(Player player, int slot, boolean top, Button button) {
        if (button == null && !top) {
            ItemStack item = player.getInventory().getItem(slot);
            if (item == null) return true;

            CustomArmor a = customArmorService.getArmor(item);
            if (a == null) return true;

            if (a.equals(requiredArmor)) {
                ItemStack next = customArmorService.getNext(item);
                if (next != null) {
                    this.performTrade(player, next);
                }
            }
            return true;
        }
        return super.shouldCancel(player, slot, top, button);
    }

    private void performTrade(Player player, ItemStack reward) {
        //Find required armor piece in player's inventory
        int itemSlot = -1;

        ItemStack[] contents = player.getInventory().getContents();
        for (int i = 0; i < contents.length; i++) {
            ItemStack stack = contents[i];

            if (stack != null && this.requiredArmor.equals(customArmorService.getArmor(stack)) && this.sameArmorPiece(stack, reward)) {
                itemSlot = i;
                break;
            }
        }

        //Perform trade
        if (itemSlot != -1 && this.subtractCompress(player.getInventory())) {
            WorldUtils.playSound(Sound.ENTITY_ARROW_HIT_PLAYER, player);
            translator.send(player, "CUSTOM_ARMOR.TRADE_MENU.TRADE", armor);
            ItemStack newItem = reward.clone();

            // Remove item
            ItemStack oldItem = player.getInventory().getItem(itemSlot);
            if (oldItem.getAmount() > 1) {
                oldItem.subtract();
                player.getInventory().addItem(newItem);
            } else {
                player.getInventory().setItem(itemSlot, null);
                player.getInventory().setItem(itemSlot, newItem);
            }

            new PlayerUpgradeArmorEvent(player, requiredArmor, armor, newItem).callEvent();
            return;
        }

        //Insufficient compress or armor
        WorldUtils.playSound(Sound.ENTITY_VILLAGER_NO, player);
        if (itemSlot == -1) {
            translator.send(player, "CUSTOM_ARMOR.TRADE_MENU.PIECE_NOT_FOUND", requiredArmor);
            return;
        }

        ItemStack compressItem = customArmorService.getArmorCompress(armor, 1);
        int count = compressCost - InventoryUtils.getItemCount(player.getInventory(), compressItem);
        translator.send(player, "CUSTOM_ARMOR.TRADE_MENU.INSUFFICIENT_COMPRESS", LocalPlaceholders.builder().add("<missing_compress>", count + "").add("<compress_name>", armor.getFormatDisplayName() + " &7&lᴄᴏᴍᴘʀᴇss"));
    }

    private boolean subtractCompress(Inventory inventory) {

        ItemStack compressItem = customArmorService.getArmorCompress(armor, 1);
        int count = InventoryUtils.getItemCount(inventory, compressItem);
        if (count < compressCost) return false;

        InventoryUtils.subtract(inventory, compressItem, compressCost);
        return true;
    }

    private boolean sameArmorPiece(ItemStack i1, ItemStack i2) {
        int id1 = customArmorService.getItemArmorId(i1);
        int id2 = customArmorService.getItemArmorId(i2);

        return id1 != -1 && id1 == id2;
    }

    @Override
    public StorageMenu.FillType getFillType() {
        return StorageMenu.FillType.ALL;
    }

    @Override
    public ItemStack getFillItem() {
        return new ItemStack(Material.BLACK_STAINED_GLASS_PANE);
    }

    @Override
    public String getTitle(Player player) {
        return armor.getFormatDisplayName() + CC.translate( " &7trade");
    }

    @Override
    public boolean isAutoUpdate() {
        return false;
    }

    @Override
    public @Nullable CoreGeyserMenu<?, ?, ?> getGeyserMenu() {
        return new CustomTradeGeyserMenu(services, armor);
    }
}
