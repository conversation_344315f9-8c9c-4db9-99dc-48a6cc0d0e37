package org.contrum.prisma.commands.user;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.command.ConsoleCommandSender;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.CCP;
import org.contrum.tritosa.Translator;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

@CommandAlias("basket")
@CommandPermission("basket.use")
public class BasketCommand extends BaseCommand {

    @Dependency private PaperServices services;
    @Dependency private Translator translator;

    @Default
    @Syntax("<nick> <context...>")
    @CommandCompletion("@players")
    public void basket(CommandSender sender, String nick, String[] context) {
        // Validate arguments
        if (context.length == 0) {
            sender.sendMessage(CCP.translate("&cUsage: /basket <nick> <context>"));
            return;
        }

        // Limit context length to prevent spam
        String message = String.join(" ", context);
        if (message.length() > 256) {
            sender.sendMessage(CCP.translate("&cMessage too long! Maximum 256 characters."));
            return;
        }

        // Get BasketName list from config
        List<String> basketNames = services.getPlugin().getConfig().getStringList("BasketName");
        
        // Validate BasketName configuration
        if (basketNames == null || basketNames.isEmpty()) {
            translator.send(sender, "ERRORS.BASKET_EMPTY_CONFIG");
            return;
        }

        // Find target player
        Player target = Bukkit.getPlayer(nick);
        if (target == null) {
            translator.send(sender, "ERRORS.BASKET_PLAYER_NOT_FOUND");
            return;
        }

        // Select random name from BasketName list
        String randomName = basketNames.get(ThreadLocalRandom.current().nextInt(basketNames.size()));
        
        // Format the final message: "&7(De <random_name>&7) <message>"
        String finalMessage = CCP.translate("&7(De " + randomName + "&7) " + message);
        
        // Send the message to the target player
        target.sendMessage(finalMessage);
        
        // Optional: Send confirmation to sender (only if not console)
        if (!(sender instanceof ConsoleCommandSender)) {
            sender.sendMessage(CCP.translate("&aMessage sent to " + target.getName() + " from " + randomName));
        }
    }
}
