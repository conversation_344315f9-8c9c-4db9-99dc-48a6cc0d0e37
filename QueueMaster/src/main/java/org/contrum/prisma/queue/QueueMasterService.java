package org.contrum.prisma.queue;

import org.contrum.prisma.QueueMaster;
import org.contrum.prisma.queue.redis.packet.QueuePlayerUpdatePacket;
import org.contrum.prisma.queue.redis.packet.QueueTickPacket;
import org.contrum.prisma.service.ServiceManager;
import org.contrum.prisma.utils.user.SimpleUser;

import java.sql.SQLOutput;
import java.util.concurrent.TimeUnit;

public class QueueMasterService extends QueueService {

    private final ServiceManager services;

    public QueueMasterService(QueueMaster queueMaster) {
        super(queueMaster);

        System.out.println("Queue system initialized");
        System.out.println("Loaded queues: " + super.getQueues().stream().map(Queue::getServer).toList());

        this.services = queueMaster;
        queueMaster.getExecutor().scheduleAtFixedRate(() -> {
            try {
                tick();
            } catch (Exception | Error e) {
                e.printStackTrace();
            }
        }, 0, 100, TimeUnit.MILLISECONDS);
    }

    public void tick() {
        StringBuilder sb = new StringBuilder();
        sb.append("\033[H\033[2J");
        sb.append("╔═══════════════════════════════╗\n");
        sb.append("║         Queue Monitor         ║\n");
        sb.append("╚═══════════════════════════════╝\n");

        for (Queue queue : super.getQueues()) {
            sb.append("[").append(queue.getServer()).append("] ");

            if (queue.isPaused()) {
                sb.append("Disabled\n");
                continue;
            }

            if (!queue.shouldTick()) {
                sb.append("Waiting\n");
                continue;
            }

            queue.setLastTick(System.currentTimeMillis());
            Queue.QueueUser user = queue.peek();

            if (user == null) {
                sb.append("Waiting (No players)\n");
                continue;
            }

            SimpleUser u = services.getServersService().getUser(user.getUuid());
            if (u == null) {
                sb.append("Offline player removed\n");
                services.getRedisBackend().sendPacket(new QueuePlayerUpdatePacket(queue.getUuid(), user, QueuePlayerUpdatePacket.Type.LEFT));
                continue;
            }

            sb.append("Sending player: ").append(u.getName()).append("\n");
            super.getRedisBackend().sendPacket(new QueueTickPacket(queue.getUuid(), queue.serializePlayers()));
        }

        // Print once per tick instead of per line
        System.out.print(sb);
        System.out.flush();
    }
}
