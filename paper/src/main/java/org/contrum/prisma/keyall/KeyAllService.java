package org.contrum.prisma.keyall;

import lombok.Getter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.config.ConfigFile;

import java.util.HashMap;
import java.util.Map;

@Getter
public class KeyAllService {

    private final PaperServices services;

    private final Map<String, KeyAll> keyAlls = new HashMap<>(); // KeyAll-Name -> KeyAll

    private ConfigFile config;

    public KeyAllService(PaperServices services) {
        this.services = services;

        this.config = new ConfigFile(services.getPlugin(), "data/keyall.yml");
        this.loadKeyAlls();
    }

    private void loadKeyAlls() {
        keyAlls.clear();

        FileConfiguration file = config.getConfig();
        ConfigurationSection sec = file.getConfigurationSection("KEY_ALLS");
        if (sec == null) {
            return;
        }

        for (String key : sec.getKeys(false)) {
            ConfigurationSection section = sec.getConfigurationSection(key);
            if (section == null) {
                continue;
            }

            KeyAll keyAll = new KeyAll(services, section);
            keyAlls.put(key, keyAll);
        }
    }

    public void reload() {
        this.config = new ConfigFile(services.getPlugin(), "data/keyall.yml");
        this.loadKeyAlls();
    }
}
