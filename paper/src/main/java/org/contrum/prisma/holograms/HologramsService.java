package org.contrum.prisma.holograms;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.SneakyThrows;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.holograms.impl.decent.DecentHologramsHandler;
import org.contrum.prisma.holograms.impl.fancy.FancyHologramsHandler;
import org.contrum.prisma.utils.PluginUtils;

public class HologramsService {

    private final PaperServices services;
    private final HologramHandlerType handlerType;

    @Getter
    private final HologramHandler handler;

    public HologramsService(PaperServices services) {
        this.services = services;

        // Define handler
        if (PluginUtils.isPluginEnabled("DecentHolograms")) {
            this.handlerType = HologramHandlerType.DECENT_HOLOGRAMS;
        } else if (PluginUtils.isPluginEnabled("FancyHolograms")) {
            this.handlerType = HologramHandlerType.FANCY_HOLOGRAMS;
        } else {
            this.handlerType = HologramHandlerType.NONE;
        }

        this.handler = handlerType.createHandler(services);
    }

    public void shutdown() {

    }

    @AllArgsConstructor
    public enum HologramHandlerType {
        DECENT_HOLOGRAMS(DecentHologramsHandler.class),
        FANCY_HOLOGRAMS(FancyHologramsHandler.class),
        NONE(null);

        private final Class<? extends HologramHandler> handlerClass;

        @SneakyThrows
        public HologramHandler createHandler(PaperServices services) {
            if (handlerClass == null) {
                return null;
            }

            return handlerClass.getDeclaredConstructor().newInstance();
        }
    }

}
