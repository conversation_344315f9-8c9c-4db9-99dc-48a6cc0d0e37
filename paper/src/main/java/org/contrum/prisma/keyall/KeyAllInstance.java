package org.contrum.prisma.keyall;

import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.tick.TickedCooldown;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class KeyAllInstance {

    private final KeyAll keyAll;
    private final Duration duration;

    private final Set<UUID> claimedPlayers = new HashSet<>();
    private final PaperServices services;
    private TickedCooldown tickedCooldown;

    public KeyAllInstance(PaperServices services, KeyAll keyAll, Duration duration) {
        this.services = services;
        this.keyAll = keyAll;
        this.duration = duration;
    }

    public void start() {
        // Start timer, menu, announcments, etc, etc!!!!


        Duration duration = Duration.ofSeconds(30);
        tickedCooldown = new TickedCooldown(services.getPlugin(), duration);
        tickedCooldown.onTickAsync((cooldown, tick) -> {

            Duration timeLeftAsDuration = cooldown.getTimeLeftAsDuration();
            System.out.println("Remaining seconds: " + timeLeftAsDuration.toSeconds());

        });

        tickedCooldown.onFinish(() -> {
            System.out.println("Finished");
        });
    }

    public void cancel() {
        if (tickedCooldown != null && tickedCooldown.isActive()) {
            tickedCooldown.cancel();
            tickedCooldown = null;
        }
    }

    public boolean isActive() {
        return tickedCooldown != null && tickedCooldown.isActive();
    }

    public boolean claim(Player player) {

        if (!this.isActive()) {
            return false;
        }

        if (!claimedPlayers.add(player.getUniqueId())) {
            return false;
        }

        // Give rewards
        KeyAllRewards rewardsFor = keyAll.getRewardsFor(player);
        if (rewardsFor == null) {
            throw new IllegalStateException("No rewards found for player " + player.getName() + " in KeyAll " + keyAll.getName() + " try adding default reward");
        }

        rewardsFor.giveRewards(player);
        return true;
    }
}
