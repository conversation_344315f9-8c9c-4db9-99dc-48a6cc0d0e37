package org.contrum.prisma.voucher;

import lombok.RequiredArgsConstructor;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;

@RequiredArgsConstructor
public class VoucherListener implements Listener {

    private final PaperServices paperServices;
    private final VoucherManager voucherManager;

    @EventHandler
    public void onClickEvent(PlayerInteractEvent event) {
        /*if (!event.getAction().name().contains("RIGHT")) {
            return;
        }*/

        Player player = event.getPlayer();
        ItemStack itemStack = player.getEquipment().getItemInMainHand();

        if (!itemStack.hasItemMeta() || itemStack.getItemMeta() == null) return;

        ItemMeta itemMeta = itemStack.getItemMeta();

        if (!itemMeta.hasDisplayName() || !itemMeta.hasLore()) {
            return;
        }

        Voucher voucher = voucherManager.getVoucherByItem(itemStack);

        if (voucher == null) {
            return;
        }

        if (!voucher.isEnabled()){
            player.sendMessage(CC.translate("&cThis voucher is currently disabled."));
            return;
        }

        event.setCancelled(true);

        Profile profile = paperServices.getProfileService().getProfile(player.getUniqueId());
        ProfilePaperMetadata metadata = profile.getServerMetadata(paperServices, ProfilePaperMetadata.class);

        if (metadata.isTagged()) {
            player.sendMessage(CC.translate("&cYou cannot use this while tagged."));
            return;
        }

        if (voucher.getCommands() != null && !voucher.getCommands().isEmpty()) {
            for (String command : voucher.getCommands()) {

                Bukkit.dispatchCommand(Bukkit.getConsoleSender(),
                        command
                                .replace("%player%", player.getName())
                                .replace("{package}", voucher.getName())
                                .replace("{voucher}", voucher.getName())
                                .replace("{player}", player.getName()));
            }


            if (voucher.getMessages() != null && !voucher.getMessages().isEmpty()) {
                for (String message : voucher.getMessages()) {
                    player.sendMessage(CC.translate(message
                            .replace("{player}", player.getName()
                            .replace("{voucher}", voucher.getName())
                                    .replace("{package}", voucher.getName()))));
                }
            }

            voucher.removeOne(player);

           // Bukkit.broadcastMessage(CC.translate("&a&lVoucher &7»" + player.getDisplayName() + " &ehas redeemed a &d&l" + voucher.getName() + " &e!"));
        }else {
            player.sendMessage(CC.translate("&cThis voucher has no commands. Please contact a staff member."));
        }
    }

}
