ERRORS:
  NO_PERMISSIONS: "<red>You don't have permissions to do this!"
  COULDNT_FIND_USER: "<red>Couldn't find a user with that name."
  INVALID_POSITION_DATA: "<red>Please specify at least x,y,z coordinates."
  INVALID_WORLD: "<red>Couldn't find a world with that name."
  RANK_ALREADY_EXISTS: "<red>A rank with that name already exists."
  RANK_DOES_NOT_EXIST: "<red>A rank with that name does not exist."
  RANK_TOO_LOW: "<red>You can't grant a rank with a higher weight than your own."
  SERVER_DOES_NOT_EXIST: "<red>Couldn't find a server with that name."
  QUEUE_ALREADY_IN_QUEUE: "<red>You're already in a queue."
  QUEUE_SERVER_NOT_FOUND: "<red>Couldn't find a queue with that name."
  QUEUE_NOT_IN_QUEUE: "<red>You're not in a queue."
  INVALID_MATERIAL: "<red>Couldn't find an item with that name."
  INVALID_NUMBER: "<red>That's not a valid number."
  INVALID_DURATION: "<red>That's not a valid duration."
  USER_NOT_FOUND: "<red>User not found!"
  USER_ALREADY_MUTED: "<red><user_coloredname> <red>is already muted."
  USER_ALREADY_BANNED: "<red><user_coloredname> <red>is already banned."
  USER_ALREADY_BLACKLISTED: "<red><user_coloredname> <red>is already <dark_red>blacklisted<red>."
  USER_NOT_MUTED: "<red>That user is not muted."
  USER_NOT_BANNED: "<red>That user is not banned."
  USER_NOT_BLACKLISTED: "<red>That user is not <dark_red>blacklisted<red>."
  USER_MUTED: "<red>You are currently muted, you will be able to talk in {punishment_if:permanent:Never,<punishment_expiry_full>}."
  CHAT_SLOWED: "<red>The chat is currently slowed, buy a rank @ <light_purple>server.gg/store <red>to chat faster or wait <primary><bold><chat_cooldown_short><red>."
  GLOBAL_CHAT_MUTED: "<red>The global chat is currently muted."
  PLAYER_PRIVATE_MESSAGES_OFF: "<red>This player has disabled private messages!"
  ABILITY_DISABLED: "<red>Este item está deshabilitado!"
  TARGET_IS_DISGUISED: "<red>El jugador es un disguise! El nick real es: &e<user_real_name>"
  SAFE_ZONE: "<red>No puedes hacer esto en una zona segura!"
  INVENTORY_CANNOT_BE_CACHED: "<red>No puedes hacer esto ahora mismo!"
  BASKET_PLAYER_NOT_FOUND: "<red>Player not found or offline!"
  BASKET_EMPTY_CONFIG: "<red>Basket command is disabled - no names configured!"
PROMPT:
  CANCELLED: "<red>Cancelled."
COMMANDS:
  DISABLED: "<red>This command is currently disabled!"
  QUEUE_JOINED: "<primary>You've joined the queue <secondary><queue_name><primary>."
  QUEUE_SERVER_OFFLINE: "<red>The server you're trying to join is offline."
  QUEUE_SERVER_WHITELISTED: "<red>The server you're trying to join is whitelisted."
  QUEUE_LEFT: "<primary>You've left the queue."
  QUEUE_HELP:
    - "<primary>/queue join <secondary><queue_name> <primary>- <secondary>Join a queue."
    - "<primary>/queue leave <primary>- <secondary>Leave the queue."
  QUEUE_ALREADY_IN_SERVER: "<red>You're already in that server."
  MEDIA:
    LINK_BAD_FORMAT: "<red>The link format is incorrect."
    COOLDOWN: "<red>You must wait <bold><cooldown></bold> <red>to send another link."
  GAMEMODE_CREATIVE_OWN: "<primary>Your gamemode was updated to <secondary>Creative<primary>."
  GAMEMODE_CREATIVE_OTHER: "<primary>You've updated <user_coloredname><primary>'s gamemode to <secondary>Creative<primary>."
  GAMEMODE_SURVIVAL_OWN: "<primary>You've gamemode was updated to <secondary>Survival<primary>."
  GAMEMODE_SURVIVAL_OTHER: "<primary>You've updated <user_coloredname><primary>'s gamemode to <secondary>Survival<primary>."
  GAMEMODE_ADVENTURE_OWN: "<primary>Your gamemode was updated to <secondary>Adventure<primary>."
  GAMEMODE_ADVENTURE_OTHER: "<primary>You've updated <user_coloredname><primary>'s gamemode to <secondary>Adventure<primary>."
  GAMEMODE_SPECTATOR_OWN: "<primary>Your gamemode was updated to <secondary>Spectator<primary>."
  GAMEMODE_SPECTATOR_OTHER: "<primary>You've updated <user_coloredname><primary>'s gamemode to <secondary>Spectator<primary>."

  LIST_FORMAT:
    - '    '
    - '&7(<ranks>&7)'
    - '&7(<online>&7/<max_players>&7) <names>'
    - '  '

  PING_SELF_HIGH: "<primary>Your ping is <red><ping>ms."
  PING_SELF_MEDIUM: "<primary>Your ping is <yellow><ping>ms"
  PING_SELF_LOW: "<primary>Your ping is <green><ping>ms"
  PING_OTHER_HIGH: "<primary><user_coloredname><primary>'s ping is <red><ping>ms"
  PING_OTHER_MEDIUM: "<primary><user_coloredname><primary>'s ping is <yellow><ping>ms"
  PING_OTHER_LOW: "<primary><user_coloredname><primary>'s ping is <green><ping>ms"

  RANK_CREATE_SUCCESS: "<primary>You've created the rank <secondary><rank_name><primary>."
  RANK_DELETE_SUCCESS: "<primary>You've deleted the rank <secondary><rank_name><primary>."
  RANK_LIST_HEADER: "<primary>Rank List:"
  RANK_LIST_ENTRY: "<primary>- <secondary><rank_name><primary>: <secondary><rank_prefix><primary> <gray>( Weight: <rank_weight> )"
  RANK_INFO_HEADER: "<primary>Rank Information:"
  RANK_INFO:
    - "<primary>Name: <secondary><rank_name>"
    - "<primary>Prefix: <secondary><rank_prefix>"
    - "<primary>Suffix: <secondary><rank_suffix>"
    - "<primary>Color: <secondary><rank_color>"
    - "<primary>Permissions: <secondary><rank_permissions>"
    - "<primary>Default: <secondary><rank_default>"
    - "<primary>Weight: <secondary><rank_weight>"
  RANK_SET_WEIGHT_SUCCESS: "<primary>You've set the weight of <secondary><rank_name><primary> to <secondary><rank_weight><primary>."
  RANK_SET_PREFIX_SUCCESS: "<primary>You've set the prefix of <secondary><rank_name><primary> to <secondary><rank_prefix><primary>."
  RANK_SET_SUFFIX_SUCCESS: "<primary>You've set the suffix of <secondary><rank_name><primary> to <secondary><rank_suffix><primary>."
  RANK_SET_COLOR_SUCCESS: "<primary>You've set the color of <secondary><rank_name><primary> to <secondary><rank_color><primary>."
  RANK_ADD_PERMISSION_SUCCESS: "<primary>You've added the permission <secondary><permission><primary> to <secondary><rank_name><primary>."
  RANK_REMOVE_PERMISSION_SUCCESS: "<primary>You've removed the permission <secondary><permission><primary> from <secondary><rank_name><primary>."
  RANK_DOESNT_HAVE_PERMISSION: "<secondary><rank_name><primary> doesn't have the permission <secondary><permission><primary>."
  RANK_ALREADY_HAS_PERMISSION: "<secondary><rank_name><primary> already has the permission <secondary><permission><primary>."
  RANK_SET_DEFAULT_SUCCESS: "<primary>You've set <secondary><rank_name><primary> as the default rank."
  RANK_INHERIT_ALREADY_EXISTS: "<red><rank_name> already inherits from <rank_parent>."
  RANK_INHERIT_HIGHER: "<red><rank_name> is lower than <rank_parent>."
  RANK_ADD_INHERIT_SUCCESS: "<green>Added inherit <yellow><rank_parent><green> to <green><rank_name>"
  RANK_HELP:
    - "<primary>/rank create <secondary><rank_name> <primary>- <secondary>Create a new rank."
    - "<primary>/rank delete <secondary><rank_name> <primary>- <secondary>Delete a rank."
    - "<primary>/rank list <primary>- <secondary>List all ranks."
    - "<primary>/rank info <secondary><rank_name> <primary>- <secondary>Get information about a rank."
    - "<primary>/rank setweight <secondary><rank_name> <secondary><rank_weight> <primary>- <secondary>Set the weight of a rank."
    - "<primary>/rank setprefix <secondary><rank_name> <secondary><rank_prefix> <primary>- <secondary>Set the prefix of a rank."
    - "<primary>/rank setsuffix <secondary><rank_name> <secondary><rank_suffix> <primary>- <secondary>Set the suffix of a rank."
    - "<primary>/rank setcolor <secondary><rank_name> <secondary><rank_color> <primary>- <secondary>Set the color of a rank."
    - "<primary>/rank addpermission <secondary><rank_name> <secondary><permission> <primary>- <secondary>Add a permission to a rank."
    - "<primary>/rank removepermission <secondary><rank_name> <secondary><permission> <primary>- <secondary>Remove a permission from a rank."
  TELEPORTALL:
    CONFIRMATION:
      - "<primary>Are you sure you want to teleport <yellow><count> players <primary>to your location?"
      - "<click:run_command:/teleportall confirm><hover:show_text:\"<green>Click to confirm\"><green>[Confirm]</hover></click> <primary>or <click:run_command:/teleportall cancel><hover:show_text:\"<red>Click to cancel\"><red>[Cancel]</hover></click>"
    TELEPORTED: "<green>You've teleported <yellow><count> players <green>to your location."
    TELEPORTED_TO: "<green>You were teleported to <yellow><user_name><green>."
    NOT_REQUESTED: "<red>You have not requested to teleport all players.<primary> Use <yellow>/teleportall <primary>to request it."
    CANCELLED: "<red>You've cancelled the teleport request."
  TELEPORTED_TO_PLAYER: "<primary>You were teleported to <user_coloredname><primary>."
  TELEPORTED_PLAYER_HERE: "<primary>You teleported <user_coloredname> <primary>to your location."
  FORCE_TELEPORTED_TO_PLAYER: "<primary>You were teleported to <user_coloredname><primary>."

  GRANT:
    RECEIVED: "<primary>You've been granted the rank <secondary><rank_name><primary>."
    SUCCESS: "<primary>You've granted <user_coloredname> <secondary><rank_name><primary>."
    REMOVED: "<primary>Your grant for <secondary><rank_name> <red>has been removed."
    REMOVED_TARGET: "<red>Removed grant for <primary><user_name>"
    CANNOT_REMOVE_DEFAULT: "<red>You cannot remove a default rank!"
    CANNOT_REMOVE_HIGHER: "<red>You cannot remove a rank higher than your own!"
    PROMPT_REASON: "<primary>Please enter a reason for removing this grant. Or type <red>cancel <primary>to cancel."

  KIT:
    CREATED: "<primary>You have created a new kit <secondary><kit_name><primary>."
    DELETE: "<primary>You have deleted the kit <secondary><kit_name><primary>."
    ALREADY_EXIST: "<red>A kit with that name already exists!"
    ARMOR_SET: "<primary>You have set the armor of the kit <secondary><kit_name><primary>."
    INVENTORY_SET: "<primary>You have set the inventory of the kit <secondary><kit_name><primary>."
    COOLDOWN_SET: "<primary>You have set the cooldown of the kit <secondary><kit_name><primary>."
    DISPLAY_NAME_SET: "<primary>You have set the cooldown of the kit <secondary><kit_name><primary>."
    ICON_SET: "<primary>You have set the icon of the kit <secondary><kit_name><primary>."
    MAX_USES_SET: "<primary>You have set the max uses of the kit <secondary><kit_name><primary>."
    PLAYTIME_SET: "<primary>You have set the required playtime of the kit <secondary><kit_name><primary>."
    SLOT_SET: "<primary>You have set the slot of the kit <secondary><kit_name><primary>."
    TYPE_SET: "<primary>You have set the type of the kit <secondary><kit_name><primary>."
    IN_MENU_SET: "<primary>You have <green>enabled <primary>the kit <secondary><kit_name><primary>."
    IN_MENU_DISABLE: "<primary>You have <red>disabled <primary>the kit <secondary><kit_name><primary>."
    RESTRICTED_SET: "<primary>You have set the kit <secondary><kit_name><primary> to restricted."
    RESTRICTED_DISABLE: "<primary>You have set the kit <secondary><kit_name><primary> to unrestricted."
    INVALID_TYPE: "<red>That is not a valid kit type."
    NO_KITS_TO_SAVE: "<red>There are not kits to save!"
    SAVED: "<green>You have saved all kits."

    CANNOT_USE: "<red>You cannot use this kit."
    NO_PERMISSION: "<red>You must buy this kit at <primary>tienda.prismamc.net<red>."
    DOESNT_EXIST: "<red>A kit with that name does not exist."
    INVENTORY_NOT_EMPTY: "<red>You inventory isn't empty! <primary>This is your last warn. <secondary>Click again to confirm!"
    NO_USES: "<red>You have used this kit too many times."
    NO_PLAYTIME: "<red>You must play for at least <duration_full> to use this kit."
    ON_COOLDOWN: "<redYou must wait <duration_full> to use this kit."
    KIT_COOLDOWN: "<red>You can't use this kit for another <secondary><duration_full>"
    KIT_EQUIPPED:
      - "&f"
      - "<primary>Successfully equipped &6&l<kit_name> &ekit!"
      - "&f"
    APPLY_TARGET: "<primary>You have applied the kit <secondary><kit_name> <primary>to <secondary><user_name><primary>."
    RECEIVED: "<primary>You have received the kit <secondary><kit_name><primary>."

  WARPS:
    CREATED: "<primary>You have created a warp with the name <secondary><warp_name><primary>!"
    DELETED: "<primary>You have deleted a warp with the name <secondary><warp_name><primary>!"
    LOCATION_SET: "<primary>You have set the location of the warp <secondary><warp_name><primary>!"
    ICON_SET: "<primary>You have set the icon of the warp <secondary><warp_name><primary>!"
    SLOT_SET: "<primary>You have set the slot of the warp <secondary><warp_name><primary>!"
    DOESNT_EXIST: "<red>A warp with that name already exists!"
    ALREADY_EXIST: "<red>A warp with that name already exists!"

    NO_LOCATION: "<red>This warp has no location set. Please contact a staff member."
    NO_PERMISSION: "<red>You do not have permission to warp to this location."
    TELEPORT: "<primary>Teleporting to <secondary><warp_name><primary>..."

  REDEEM:
    RESET: "<primary>Successfully reset all redeem votes on all servers!"
    VOTE:
      - "&f"
      - "&b&lᴀᴘᴏʏᴏ &7➜ &fʜᴀs ᴠᴏᴛᴀᴅᴏ ᴘᴏʀ &b&l<redeem_name>&f! &7&o ( /ᴀᴘᴏʏᴀʀ )"
      - "&f"

  TELEPORTED_WORLD: "<primary>You were teleported to <secondary><world><primary>."
  TELEPORTED_EXACT: "<primary>You were teleported to <secondary><x>, <y>, <z><primary>."
  BROADCAST:
    HEADER: "<primary>[Broadcast] <secondary><user_coloredname><primary>:"
    FOOTER: "<primary>Message: <secondary><message>"
  CANNOT_PUNISH_COMBAT: "<red>No puedes banear a este usuario porque está en combate!"
  BAN_PLAYER: "<primary>You've banned <user_coloredname><primary>."
  KICK_PLAYER: "<primary>You've kicked <user_coloredname><primary>."
  BLACKLIST_PLAYER: "<primary>You've blacklisted <user_coloredname><primary>."
  MUTE_PLAYER: "<primary>You've muted <user_coloredname><primary>."
  WARN_PLAYER: "<primary>You've warned <user_coloredname><primary>."
  UNBAN_PLAYER: "<primary>You've unbanned <user_coloredname><primary>."
  UNBLACKLIST_PLAYER: "<primary>You've unblacklisted <user_coloredname><primary>."
  UNMUTE_PLAYER: "<primary>You've unmuted <user_coloredname><primary>."
  SPAWN_LIST:
    - "<primary>Available spawns"
  SPAWN_LIST_ENTRY: "  <primary>World: <secondary><world> <primary>Spawn: <secondary><click:run_command:/tppos <teleport>><hover:show_text:'<blue>Click to teleport'> <spawn> </click> <gray>- <click:run_command:/removespawn <world>> <hover:show_text:'<red>Click to delete'><red>[X]</red></click>"
  VOTE:
    THANKS: "<primary>Thanks for voting!"
    ALREADY_VOTED: "<red>You've already voted today."
    VOTE_MESSAGE: "<primary>Vote for us at <secondary>prismamc.net/vote<primary> to get rewards!"
  SOCIAL:
    TEAMSPEAK: "teamspeak"
    TELEGRAM: "telegram"
    WEBSITE: "website.com"
    TWITTER: "twitter.com"
    INSTAGRAM: "instagram.com"
    DISCORD: "discord.com"
    SOCIALS:
      - "twitter: twitter.com"
      - "instagram: instagram.com"
      - "discord: discord.com"
      - "teamspeak: teamspeak"
      - "telegram: telegram"
      - "website: website.com"
  BACK:
    NO_LOCATION: "<red>No location to go back to."
    TELEPORTED: "<primary>You've been teleported back to your previous location."
  SKULL_ADDED: "<primary>You've added <secondary><owner><primary>'s head to your inventory."
  HELPOP_COMMAND_COOLDOWN: "<red>Please wait <bold><duration_full></bold> <red>to request help again."
  HELPOP_COMMAND_RECEIVED: "<green>We've received your request, any online staff member will shortly help you."
  HELPOP_COMMAND_NOTIFICATION:
    - '<hover:show_text:"<green>Click to teleport."><click:run_command:"/joinortp <user_name>"><primary>[Helpop] <user_coloredname> <yellow>requested help <gray>(<user_server>):'
    - "    <primary>Reason<gray>: <gray><reason>"
  ADDED_LORE: "<primary>You successfully added the line to the item."
  REMOVED_LORE: "<primary>You successfully removed <secondary><line> line <primary>from the item."
  SET_LORE_LINE: "<primary>You successfully changed the <secondary><line> line <primary>from the item."
  SET_ITEM_TYPE: "<primary>You successfully changed the item type to <secondary><type><primary>."
  SET_DURABILITY: "<primary>You successfully changed the durability of the item."
  PLAYER_HEAD: "<primary>You have gotten the head of <secondary><player><primary>."

  ENCHANT_SUCCESS: "<primary>You have enchanted <user_coloredname><primary>'s item with <secondary><enchantment> <level><primary>."
  ENCHANT_SUCCESS_SELF: "<primary>Your item is now enchanted with <secondary><enchantment> <level><primary>."

  MORE_COMMAND: "<primary>There you go..."

  FILL_BOTTLE:
    MUST_HOLDING: "<red>You must be holding a glass bottle to fill it with water."
    FILLED: "<green>You have filled your bottle with water."
  REPAIRED_ITEM: "<primary>You repaired your item."
  REPAIRED_ALL: "<primary>You successfully repaired all your items."

  GIVE_OTHER_ITEM: "<primary>You gave <user_coloredname> <secondary>x<amount> <type><primary>."
  GIVE_RECEIVE_ITEM: "<primary>You received <secondary>x<amount> <type><primary>."

  GIVE_ALL_ITEM: "<primary>You gave everyone <secondary>x<amount> <type><primary>."
  RENAMED_ITEM: "<primary>You have renamed your item."
  MUTE_CHAT:
    MUTED: "&cThe global chat has been muted."
    UN_MUTED: "&aThe global chat has been un-muted."
  SLOW_MODE: "<red>Public chat was slowed by <changed_by_displayname><red>."
  SLOW_MODE_OFF: "<green>Public chat is no longer slowed."
  CLEAR_CHAT:
    CLEARED: "<yellow>The public-chat has been <red>cleared <yellow>by <gold><player><yellow>."
  SUDO:
    PERMISSION_ERROR: "&cYou don't have permission to use this command."
    SUCCESS: "&aYou've executed the command as <user_coloredname>."
    SUCCESS_ALL: "&aYou've made everyone on this server say the following: <message>"
  GOD_MODE:
    ENABLED: "<green>You've enabled god mode for <yellow><user_name>."
    DISABLED: "<red>You've disabled god mode for <yellow><user_name>."
  ITEM:
    ERROR: "&cYou must be holding an item to use this command."
    UNBREAKABLE: "&aYou've set the item to unbreakable."
  CLEAR:
    SELF: "<primary>Inventory cleared."
    OTHER: "<secondary><user_name> <primary>inventory has been cleared."
  FLY:
    ENABLED: "<primary>Flight mode enabled."
    DISABLED: "<red>Flight mode disabled."
    ENABLED_OTHER: "<primary>Flight mode of <secondary><user_name> <primary>enabled."
    DISABLED_OTHER: "<red>Flight mode of <secondary><user_name> <red>disabled."
  HEAL:
    HEALED: "<primary>Your health has been filled!"
    HEALED_OTHER: "<primary><user_name> health has been filled!"
  FEED:
    FEED: "<primary>Your hunger has been filled!"
  KOTH:
    NOT_FOUND: "<red>Koth not found!"
    KOTH_CREATED: "<primary>You have created a new koth named <secondary><koth_name>!"
    KOTH_REMOVED: "<red>You have deleted the koth <secondary><koth_name>!"

    STOPPED: "<red>KoTH has been stopped!"

    NOT_ACTIVE: "<red>This KoTH is not active!"
    ALREADY_ACTIVE: "<red>KoTH is already active!"
    INVALID_SELECTION: "<red>Invalid world edit selection!"
    KOTH_SET_START_EVERY: "<primary>Koth start every time has been set to <secondary><duration_short>!"
    KOTH_SET_DURATION: "<primary>Koth camp duration has been set to <secondary><duration_short>!"
    KOTH_SET_ZONE: "<primary>Koth zone updated!"
    KOTH_REWARDS_EMPTY: "<red>This koth has no rewards!"
    KOTH_REWARDS_ADDED: "<primary>You have added a reward to the koth!"
  ALTS:
    LOADING_ALTS: "<primary>Buscando alts..."
    NO_ALTS: "<primary><user_name> <red>no tiene alts registradas!"
  BATTLE_PASS:
    QUESTS:
      DOESNT_EXISTS: "&cNo existe una mision con ese nombre!"
    PRESTIGE:
      GET: "&eEl prestigio de <user_name> es de: &a<prestige>"
      SET: "&aEl prestigio de &e<user_name>&a ahora es de: &d<prestige>"
    LEVEL:
      GET: "&eEl nivel del battlepass de <user_name> es de: &a<level>"
      SET: "&aEl nivel del battlepass de &e<user_name>&a ahora es de: &d<level>"
  RALLY:
    NO_CLAN: "&cDebes tener un clan para poder poner un rally!"
    WAYPOINT: "&3&lᴄʟᴀɴ&r &7| &b<user_name> &eʜᴀ ᴘᴜᴇsᴛᴏ ᴜɴ ʀᴀʟʟʏ! &7(<location>&7)"
  DROP_MULTIPLIER:
    ORB_ENABLED: "&aHas activado el orbe de tu multiplicador!"
    ORB_DISABLED: "&cHas ocultado el orbe de tu multiplicador!"
  NEWBIE_PROTECTION:
    PROTECTED: "&a&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fTienes una protección activa durante &e<duration_full>!"
    NOT_PROTECTED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fno tienes ninguna protección activa."

    DISABLED: "&4&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fHas &c&l&ndeshabilitado&r&f tu protección al PVP!&f Ten cuidado, &eya puedes &n&eMORIR&r&e."

    TARGET_ALREADY_PROTECTED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &e<user_name>&f ya tiene una protección activa."
    TARGET_NOT_PROTECTED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &e<user_name>&f no tiene ninguna protección activa."
    ENABLED_TO: "&a&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fHas &a&lhabilitado&r&f la protección al PVP de &e<user_name>&f."
    DISABLED_TO: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fHas &c&l&ndeshabilitado&r&f la protección al PVP de &e<user_name>&f."
  PUNISHMENTS:
    TARGET_IS_DISGUISED: "<red>No puedes sancionar a un nick de disguise! El nick real es: &e<user_real_name>"
  NOTES:
    ADDED: "&aHas agregado una nota a <user_real_name>!"
    REMOVED: "&cHas eliminado una nota de <user_real_name>!"
    CHECK: "&eBuscando notas de <user_real_name>..."
  JOIN_ME:
    ON_COOLDOWN: "<red>You must wait <yellow><duration_short><red> to use this command again!"
    ALERT: "&e<user_name> wants you to join his game! <gray>Click to join (<server_name>)"
    ALERT_HOVER: "<gray>Click to join!"
  BALANCE:
    CHECK_OTHER: "&aEl balance de <user_name> es de: &6<balance> coins"
  BALANCE_TOP:
    EMPTY_PAGE: "&cNo hay jugadores en esta pagina aún!"
    INVALID_PAGE: "&cHas ingresado un número de página invalido!"
    HEADER: "&7--- &eTop de Coins &8( Pag: <page> ) &7---"
    PLAYER: "&7<rank>. &e<user_name> &8➜ &6<balance> ⛃"
    FOOTER: "&7--- &eTop de Coins &8( Pag: <page> ) &7---"
  REPORT:
    CANT_REPORT_SELF: "<red>No puedes reportarte a ti mismo!"
    SENT: '&aHas reportado al jugador &e<user_name>'
    COOLDOWN: "&cEspera &e<duration_full>&c antes de hacer otro reporte!"
    STAFF_BROADCAST:
      - ''
      - '<red>[Report] <yellow><user_name> <white>ha reportado a <red><target_name> <gray>(<server_name>):'
      - '    <primary>Razón<gray>: <gray><reason>'
      - ''
  PAY:
    TARGET_NOT_FOUND: "<red>El jugador no está conectado."
    INVALID_AMOUNT: "<red>No puedes pagar una cantidad negativa o cero."
    NOT_ENOUGH_MONEY: "<red>No tienes suficiente dinero para pagar a <user_name>."
    TARGET_DOES_NOT_ACCEPT_PAYMENTS: "<red><user_name> <red>no acepta pagos."
    COINS_SENT: "<green>Has enviado <gold><coins_amount> coins <green>a <yellow><user_name>."
    COINS_RECEIVED: "<green><user_name> <green>te ha enviado <gold><coins_amount> coins."
  ORDEN:
    - "&5&l                   ᴏʀᴅᴇɴ ᴅᴇ ᴍɪɴᴀs"
    - "&7"
    - "&7                             ᴍᴀʀɪᴏ (II)"
    - "&7"
    - "&e1. &f&lɪɴɪᴄɪᴀʟ &d◆ &aᴢᴏɴᴀ ꜱᴇɢᴜʀᴀ &d◆ &fᴇɴ &e%realmines_secondsleft_Inicial% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e2. &a&lʟᴜɪɢɪ &d◆ &aᴢᴏɴᴀ ꜱᴇɢᴜʀᴀ &d◆ &fᴇɴ &e%realmines_secondsleft_Luigi% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e3. &d&lᴘᴇᴀᴄʜ &d◆ &aᴢᴏɴᴀ ꜱᴇɢᴜʀᴀ &d◆ &fᴇɴ &e%realmines_secondsleft_Peach% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e4. &#FFE576&lᴅ&#FFDD59&lᴀ&#FED43B&lɪ&#FECC1E&ls&#FDC300&lʏ &d◆ &aᴢᴏɴᴀ ꜱᴇɢᴜʀᴀ &d◆ &fᴇɴ &e%realmines_secondsleft_Daisy% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e5. &#725637&lɢ&#6D5534&lᴏ&#695331&lᴏ&#64522D&lᴍ&#60502A&lʙ&#5B4F27&lᴀ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Goomba% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e6. &2&lʏᴏsʜɪ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Yoshi% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e7. &f&lʙᴏᴏ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Boo% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e8. &5&lᴡᴀʟᴜɪɢɪ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Waluigi% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e9. &e&lᴡᴀʀɪᴏ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Wario% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e10. &2&lᴋᴏᴏᴘᴀ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Koopa% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e11. &#F35D5D&lᴛ&#ED6E6E&lᴏ&#E68080&lᴀ&#E09191&lᴅ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Toad% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e12. &#8F6141&lᴅ&#9B6F48&lᴏ&#A77D4F&lɴ&#B38B56&lᴋ&#BF995D&lᴇ&#CCA765&lʏ &#E4C373&lᴋ&#F0D17A&lᴏ&#FCDF81&lɴ&#FCDF81&lɢ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Dkong% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e13. &#468538&lʙ&#5F8C35&lᴏ&#789232&lᴡ&#919930&ls&#AA9F2D&lᴇ&#C3A62A&lʀ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Bowser% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e14. &#CB3B52&lᴍ&#D8495A&lᴀ&#E55762&lʀ&#F2646A&lɪ&#FF7272&lᴏ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_Mario% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e15. &x&B&8&A&E&A&E&lʙ&x&C&0&A&3&A&3&lᴏ&x&C&8&9&9&9&9&lᴡ&x&D&0&8&E&8&E&lꜱ&x&D&7&8&4&8&4&lɪ&x&D&F&7&9&7&9&lᴛ&x&E&7&6&F&6&F&lᴏ&x&E&F&6&4&6&4&lꜱ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_MinaBowsitos% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&e16. &x&F&F&E&E&0&0&lᴇ&x&F&F&F&0&1&A&lꜱ&x&F&F&F&1&3&3&lᴛ&x&F&F&F&3&4&D&lʀ&x&F&F&F&5&6&7&lᴇ&x&F&F&F&7&8&1&lʟ&x&F&F&F&8&9&A&lʟ&x&F&F&F&A&B&4&lᴀ &d◆ &cᴢᴏɴᴀ ᴘᴠᴘ &d◆ &fᴇɴ &e%realmines_secondsleft_MinaEstrella% ꜱᴇɢᴜɴᴅᴏꜱ"
    - "&7"
MENUS:
  GRANT:
    RANK_ITEM_NAME: "<rank_display_name>"
    RANK_ITEM_LORE:
      - ""
      - "&7Click para elegir el rango <rank_color><rank_name>&7!"

    SERVER_ITEM_NAME: "&e&l<server_name>"
    SERVER_ITEM_LORE:
      - ""
      - "&7Click para elegir el servidor &c<server_name>&7!"

    DURATION_ITEM_NAME: "&e<duration_short>"
    DURATION_ITEM_LORE:
      - ""
      - "&7Click para elegir una duracion de &c<duration_short>&7!"

    CUSTOM_DURATION_NAME: "&eOtra."
    CUSTOM_DURATION_LORE:
      - ""
      - "&7Click para elegir otra &eduracion&7!"
  ALTS:
    ALT_REMOVED: "&aHas eliminado la alt <user_name>"
    ALT_ITEM_NAME: "&b▎ &e<user_name>"
    ALT_ITEM_LORE:
      - ""
      - "&eSancionado: &c<user_is_punished>"
      - "&eRango: &c<rank_display_name>"
      - "&eUltima conexión: &cHace <user_last_seen>"
      - ""
      - "&eClick izquierdo para &aver."
    ALT_REMOVE_LORE:
      - "&eClick derecho para &cremover."
  VIRTUAL_SHOP:
    NO_PERMISSION_LORE:
      - ""
      - "&b&l| &r&cAdquiere un rango en &b&ntienda.prismamc.net&c"
      - "&b&l| &r&cPara intercambiar remotamente!"
    ALLOWED_LORE:
      - ""
      - "&b&l| &r&aClick para intercambiar!"
    NO_PERMISSION_MESSAGE: "&c&cAdquiere un rango en &b&ntienda.prismamc.net&c para poder intercambiar con los NPC remotamente!"
  BATTLE_PASS:
    EPIC_REWARD_NAME: "&5Dia #<day>"
    REWARD_NAME: "&eDia #<day>"
    CLAIMED_NAME: "&7Dia #<day>"
    REWARD_LORE:
      - ""
      - "&eCoste: &a<cost> prestigios"
      - ""
      - "&eRewards:"
    CLAIMED_LORE:
      - ""
      - "&aReclamada!"
      - ""
      - "&eRewards:"
    EMERALD:
      NAME: "&a&l¿ᴄᴏᴍᴏ ᴜsᴀʀ?"
      LORE:
        - "&8ʙᴀᴛᴛʟᴇᴘᴀss"
        - "&f"
        - "&f &b▎ &fᴄᴜᴍᴘʟᴇ ᴛᴜs ᴍɪsɪᴏɴᴇs ᴅɪᴀʀɪᴀᴍᴇɴᴛᴇ,"
        - "&f &b▎ &fᴀᴄᴜᴍᴜʟᴀ &aʀᴇᴘᴜᴛᴀᴄɪᴏɴ&f ʏ ʀᴇᴄʟᴀᴍᴀ ʟᴀs ʀᴇᴄᴏᴍᴘᴇɴsᴀs"
        - "&f"
        - "&2♦ &2&lʀᴇᴘᴜᴛᴀᴄɪᴏɴ: &f<prestiges>"
  QUESTS:
    DAILY_NEXT_RESTART_NAME: "&eMisiones Diarias"
    DAILY_NEXT_RESTART_LORE:
      - "&eSiguiente reinicio en: &c<duration_full>"
    DAILY_QUEST_LORE_FIRST:
      - "&8Mision"
      - ""
    DAILY_QUEST_LORE_SECOND:
      - ""
      - "&7- &fProgreso: &7<progress> &8/ &7<progress_required>"
      - ""
      - "&f&lRECOMPENSAS:"
    STATIC_QUEST_LORE_FIRST:
      - "&8Mision"
      - ""
    STATIC_QUEST_LORE_SECOND:
      - ""
      - "&7- &fProgreso: &7<progress> &8/ &7<progress_required>"
      - ""
      - "&f&lRECOMPENSAS:"
    CLICK_TO_CLAIM_LORE:
      - ""
      - "&aClick para reclamar!"
  DISGUISED_PLAYERS:
    PLAYER_ITEM_NAME: "&e<user_displayname> &7( <user_real_name> )"
    PLAYER_ITEM_LORE:
      - ""
      - "&b- &eNombre del disguise: &c<user_name>"
      - "&b- &eNombre real: &c<user_real_name>"
      - ""
      - "&cClick izquierdo para remover disguise."
  DISGUISE_LOGS:
    DISGUISE_LOG_ITEM_NAME: "&eplayer <action>."
    DISGUISE_LOG_ITEM_LORE:
      - ""
      - "&eNew nickname: &b<new_nickname>"
      - "&eOld nickname: &b<old_nickname>"
      - "&eTime: &c<formatted_time>"
  PROFILE:
    BUTTONS:
      RANDOM:
        NAME: '&b🎲 Random'
        LORE:
          - ''
          - '&f• &7Selecciona una arena aleatoria.'
          - ''
          - '&e¡Click para elegir!'
      SELECT_ARENA:
        NAME: '&a🏟 <arena_name>'
        LORE:
          - ''
          - '&f• &7Selecciona esta arena para tu duelo.'
          - ''
          - '&e¡Click para seleccionar!'
      AVAILABLE:
        NAME: '&6🗺 Mapas'
        LORE:
          - ''
          - '&f• &7Arenas disponibles&f: &e<arenas_available>'
          - ''
      BACK:
        NAME: '&c⬅ Volver'
        LORE:
          - ""
          - "&7Click para ver las estadisticas de este usuario."
      DISGUISE:
        NAME: "&eDisguise logs"
        LORE:
          - ""
          - "&7Click para ver los logs de disguise."
      GRANTS:
        NAME: "&eGrants"
        LORE:
          - ""
          - "&eEste usuario ha tenido: &c<user_grants_count> rangos. &7(<user_active_grants_count> activos)"
          - ""
          - "&7Click para ver los rangos de este usuario."
      PUNISHMENTS:
        NAME: "&ePunishments"
        LORE:
          - ""
          - "&eEste usuario ha tenido: &c<user_punishments_count> sanciones. &7(<user_active_punishments_count> activas)"
          - ""
          - "&7Click para ver las sanciones de este usuario."
  PAY_HISTORY:
    ITEMS:
      ADD_LOG:
        NAME: "&aPago Recibido"
        LORE:
          - ""
          - "&eCantidad: &a<amount>"
          - "&ePagado por: &a<other_name>"
          - "&eBalanceBefore: &c<old_balance>"
          - "&eFecha: &a<date>"
      REMOVE_LOG:
        NAME: "&cPago Enviado"
        LORE:
          - ""
          - "&eCantidad: &c<amount>"
          - "&ePagado a: &c<other_name>"
          - "&eBalanceBefore: &c<old_balance>"
          - "&eFecha: &c<date>"
  REPORTS_HISTORY:
    ITEMS:
      LOG:
        NAME: "&cReporte Recibido"
        LORE:
          - ""
          - "&eReportado por: &c<reporter_name>"
          - "&eRazon: &c<reason>"
          - "&eServidor: &c<server>"
          - "&eFecha: &c<date>"
  FIREWORK_SPEED:
    SELECTED_LORE:
      - ""
      - "&aVelocidad seleccionada!"
    SPEED_1:
      NAME: "&eVelocidad 1"
      LORE:
        - ""
        - "&7Click para que tus cohetes sean."
        - "&7De velocidad &enivel 1."
    SPEED_2:
      NAME: "&eVelocidad 2"
      LORE:
        - ""
        - "&7Click para que tus cohetes sean."
        - "&7De velocidad &enivel 2."
    SPEED_3:
      NAME: "&eVelocidad 3"
      LORE:
        - ""
        - "&7Click para que tus cohetes sean."
        - "&7De velocidad &enivel 3."
  OUTFITS:
    OUTFIT_ITEM:
      NAME: "&e<outfit_name>"
      LORE:
        - ""
        - "&aClick izquierdo para equipar este outfit."
        - "&eClick derecho para editar."
    CREATE_ITEM:
      NAME: "&eCrear outfit"
      LORE:
        - ""
        - "&aClick para crear un outfit."
    DELETE_ITEM:
      NAME: "&cEliminar outfit"
      LORE:
        - ""
        - "&cClick para eliminar este outfit."
    USE_INVENTORY_ITEM:
      NAME: "&eUsar inventario como outfit"
      LORE:
        - ""
        - "&aClick para subir tu inventario a este outfit."
    RENAME_ITEM:
      NAME: "&eRenombrar outfit"
      LORE:
        - ""
        - "&eClick para renombrar este outfit."
    ANNIVERSARY:
      FIREWORK:
        NAME: '&d&l¡2° Aniversario!'
        LORE:
          - ''
          - '&f¡&dFeliz 2° Aniversario &fde &bPrismaMC&f!'
          - '&7Gracias por ser parte de nuestra historia ❤'
          - ''
      CAKE:
        NAME: '&d&lPastel de Aniversario'
        LORE:
          - ''
          - '&fDesbloquea &d7 misiones diarias&f con el paso de los días.'
          - '&f¡Cada una trae &b&lrecompensas únicas&f!'
          - ''
          - '&fAdemás, completa la &dmisión principal &fpara obtener un'
          - '&erango &lSORPRESA&f 🎁'
          - ''
      MAIN_MISSION:
        NAME: '&d&lMisión Principal'
        LORE:
          - ''
          - '&fHaz &eclick derecho &fsobre las &d🎂 tartas con velas'
          - '&fdistribuidas por todo el mapa.'
          - ''
          - '&f¡Reúne las 100 tartas para obtener un &5&lrango exclusivo&f!'
          - ''
          - '&7Tartas encontradas: &d<cakes>&7/&f100'
ABILITY:
  GLOBAL_COOLDOWN: '&cYou have to wait &e<duration_full> &cto use another ability!'
  ABILITY_COOLDOWN: '&cTienes que esperar &e<duration_full> &cpara usar esta habilidad
    de nuevo!!'
  SWITCHER:
    DISTANCE_EXCEEDED: '&cThe maximum distance for the switcher is <distance> blocks!'
    SWITCHED_MESSAGE: '&eYou have changed places with <player_name>!'
  ELYTRA_DISABLER:
    ELYTRAS_DISABLED_BY: '&cYour elytras have been temporally disabled by &e<player_name>
      &cduring &e<duration_full>'
    ELYTRAS_DISABLED: '&cYour elytras are temporally disabled for &e<duration_full>'
  INVISIBILITY:
    USE: '&eNow you and your armor will be temporarily invisible!'
    EXPIRED: '&cYou are now visible to other players!'
  RAGE:
    USE: '&aHas dado el efecto de &5furia &aa ti y a &e<allies> &aaliados!'
    RECEIVED_BY_ALLY: '&aHas recibido el efecto de &5furia &ade un aliado cercano!'
  HEALER:
    USE: '&aHas dado el efecto de &7curacion &aa ti y a &e<allies> &aaliados!'
    RECEIVED_BY_ALLY: '&aHas recibido el efecto de &6curacion &ade un aliado cercano!'
  LIFE_STEALER:
    ALREADY_MARKED: '&cEl jugador &e<user_name> &cya está marcado!'
    TAGGED_TO: '&aHas marcado a &e<user_name>. &aAhora robarás su vida de manera pasiva
      por unos segundos!'
    TAGGED_BY: '&e<user_name> &cha empezado a robar tu vida! Perderas vida por unos
      segundos.'
  MAGE:
    ALREADY_MARKED: '&cEl jugador &e<user_name> &cya está marcado!'
    HIT_MESSAGE: '&eGolpea a &c<user_name> &e<remaining_hits> veces mas para marcarlo!'
    MARKED_TO: '&aHas aplicado el efecto del mago a &e<user_name>'
    MARKED_BY: '&cHas sido marcado por el efecto del mago por &e<user_name>'
  SPARKY:
    USE: '&aHas electrocutado a &e<affected_players>&a jugadores!'
    STUNNED: '&cHas sido electrocutado por &e<user_name>&c!'
    STUN_FINISHED: '&aYa no estás electrocutado!'
    CANNOT_PLACE_STUNNED: '&cNo puedes colocar bloques mientras estas detenido por
      chispitas!'
    CANNOT_BREAK_STUNNED: '&cNo puedes romper bloques mientras estas detenido por
      chispitas!'
  PEKKA_SWORD:
    TOTEM_COOLDOWN: '&cNo puedes robarle los efectos a este jugador porque acaba de
      usar totem!'
    SWORD_COOLDOWN: '&cNo puedes robarle los efectos a este jugador porque acaban
      de usar la espada en el!'
    USE: '&aHas corrompido temporalmente a &e<user_name>'
    CORRUPTED: '&cAlguien ha usado la espada PEKKA en ti! Recibiras el 20% del daño
      que hagas por unos segundos.'
  ANTI_RUN:
    PROHIBITED_REGION: '&cNo puedes usar el antirun en esta zona!'
  COINS_EXCAVATOR_BOOK:
    UPGRADE_BROADCAST: "&5&lᴘɪᴄᴏ ʟᴇɢᴇɴᴅᴀʀɪᴏ &8&l➔&r &fEl jugador &b<user_name> &fha subido el nivel de &6&nExcavador de Coins&r&f de su pico a &enivel <new_level>!"
  EVOKER:
    USE: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas invocado la habilidad del &d&lEvoker&r&f!"
    POSITIVE_FANG_APPLIED: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas recibido efectos positivos de un &d&lEvoker&r&f aliado!"
    NEGATIVE_FANG_APPLIED: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &cHas sido mordido por un &d&lEvoker&r&c enemigo! &7&oAplicando efectos negativos..."
  BAT_BITE:
    BITTEN_TO: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas mordido a &e<user_name>&f con la &c&lMordedura Letal&r&f."
    BITTEN_BY: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &cHas sido mordido por &e<user_name>&c con la &c&lMordedura Letal&r&f."
  GLORY_OF_GOLEM:
    USE: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas invocado a la &c&lGloria del Golem&r&f! Serás &einmune al daño&f por <duration_short>."
    IMMUNITY_ENDED: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &cTu inmunidad ha terminado. &c&nYa puedes morir&r&c!"
    CANNOT_USE_COMMAND_WHILE_IMMUNE: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &cNo puedes usar este comando mientras estás bajo el efecto de la &c&lGloria del Golem&r&c."
    BUFFED_BY_ALLY: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas recibido el efecto de la &c&lGloria del Golem&r&f de un aliado cercano."
    TARGET_IS_IMMUNE: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &cNo puedes dañar al jugador &e<user_name>&c! Está bajo el efecto de la &c&lGloria del Golem&r&c."
  MARTILLO:
    STUNNED_TARGET: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas aturdido a &e<user_name>&f con el &6&lMartillo Bruto&r&f."
    STUNNED_BY: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &cHas sido aturdido por &e<user_name>&c con el &6&lMartillo Bruto&r&c."
  WARDEN:
    ALREADY_ACTIVE: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &cYa tienes activa la habilidad del &5&lWarden&r&c."
    TIMER_STARTED: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas activado la habilidad del &5&lWarden&r&f. Tu tiempo ha comenzado."
    WAVE_GENERATED: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas generado una ola de sonido que aturde a los jugadores cercanos."
    SEQUENCE_COMPLETED: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas completado la secuencia de la habilidad del &5&lWarden&r&f. &4Fuerza III &factivada por &e<duration_full>."
    NO_WAVES_GENERATED: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &cNo has generado ninguna ola de sonido."

    RECEIVED_ALLY_BENEFITS: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &fHas recibido los beneficios de la habilidad del &5&lWarden&r&f de un aliado cercano."
    HIT_BY_WAVE: "<prisma_font>&d&lAbility</prisma_font>&r &8&l➔&r &cHas sido golpeado por la ola de sonido del &5&lWarden&r&c. ¡Estás aturdido!"
QUEUE:
  ACTIONBAR: "&aQueue position: <queue_position>"
  SENT: "<primary>Sending to <secondary><server_name><primary>..."
PUNISHMENTS:
  BROADCAST_WARNED: "<primary><punishment_target_coloredname><primary> has been warned for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_KICKED: "<primary><punishment_target_coloredname><primary> has been kicked for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_BANNED: "<primary><punishment_target_coloredname><primary> has been banned for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_BLACKLISTED: "<primary><punishment_target_coloredname><primary> has been blacklisted for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_MUTED: "<primary><punishment_target_coloredname><primary> has been muted for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_WARNED_PUBLIC: "<primary><punishment_target_coloredname><primary> has been warned for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_KICKED_PUBLIC: "<primary><punishment_target_coloredname><primary> has been kicked for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_BANNED_PUBLIC: "<primary><punishment_target_coloredname><primary> has been banned for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_BLACKLISTED_PUBLIC: "<primary><punishment_target_coloredname><primary> has been blacklisted for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_MUTED_PUBLIC: "<primary><punishment_target_coloredname><primary> has been muted for <secondary><punishment_reason><primary> by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_UNMUTED: "<primary><punishment_target_coloredname><primary> has been unmuted by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_UNMUTED_PUBLIC: "<primary><punishment_target_coloredname><primary> has been unmuted by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_UNBANNED: "<primary><punishment_target_coloredname><primary> has been unbanned by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_UNBANNED_PUBLIC: "<primary><punishment_target_coloredname><primary> has been unbanned by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_UNBLACKLISTED: "<primary><punishment_target_coloredname><primary> has been unblacklisted by <secondary><punishment_sender_coloredname><primary>."
  BROADCAST_UNBLACKLISTED_PUBLIC: "<primary><punishment_target_coloredname><primary> has been unblacklisted by <secondary><punishment_sender_coloredname><primary>."
  BAN_LOGIN_SCREEN:
    - ''
    - ' &cHas sido baneado de PrismaMC.'
    - ''
    - ' &7● &fRazón: &c<punishment_reason>'
    - ' &7● &fExpira: &c<punishment_expiry_full>'
    - ''
    - ' &cPuedes apelar tu sanción en &adiscord.gg/prismamc &co puedes'
    - ' &cadquirir el articulo "el perdón" en &atienda.prismamc.net&c.'
    - ''
  IP_BAN_LOGIN_SCREEN:
    - ''
    - ' &cHas sido ip baneado de PrismaMC.'
    - ''
    - ' &7● &fRazón: &c<punishment_reason>'
    - ' &7● &fExpira: &c<punishment_expiry_full>'
    - ''
    - ' &cPuedes apelar tu sanción en &adiscord.gg/prismamc &co puedes'
    - ' &cadquirir el articulo "el perdón" en &atienda.prismamc.net&c.'
    - ''
  IP_BAN_ALT_LOGIN_SCREEN:
    - ''
    - ' &cUna de tus cuentas ha sido ip baneada de PrismaMC.'
    - ''
    - ' &7● &fRazón: &c<punishment_reason>'
    - ' &7● &fCuenta: &c<alt_name>'
    - ' &7● &fExpira: &c<punishment_expiry_full>'
    - ''
    - ' &cPuedes apelar tu sanción en &adiscord.gg/prismamc &co puedes'
    - ' &cadquirir el articulo "el perdón" en &atienda.prismamc.net&c.'
    - ''
  BLACKLIST_LOGIN_SCREEN:
    - ''
    - ' &cHas sido blacklisteado de PrismaMC permanentemente.'
    - ''
    - ' &7● &fRazón: &c<punishment_reason>'
    - ' &7● &fExpira: &c<punishment_expiry_full>'
    - ''
    - ' &cPuedes apelar tu sanción en &adiscord.gg/prismamc &co puedes'
    - ' &cadquirir el articulo "el super perdón" en &atienda.prismamc.net&c.'
    - ''
  BLACKLIST_ALT_LOGIN_SCREEN:
    - ''
    - ' &cUna de tus cuentas ha sido blacklisteada de PrismaMC permanentemente.'
    - ''
    - ' &7● &fRazón: &c<punishment_reason>'
    - ' &7● &fCuenta: &c<alt_name>'
    - ' &7● &fExpira: &c<punishment_expiry_full>'
    - ''
    - ' &cPuedes apelar tu sanción en &adiscord.gg/prismamc &co puedes'
    - ' &cadquirir el articulo "el super perdón" en &atienda.prismamc.net&c.'
    - ''
  KICK_LOGIN_SCREEN:
    - "<red>You have been kicked by a staff member."
  MENU:
    DUEL_INFO:
      NAME: '&bInformacion del Duel.'
      LORE:
        - '&7'
        - '&f• &7Tú&f: &a<sender_name>'
        - '&f• &7Oponente&f: &c<target_name>'
        - '&7'
        - '&f• &7Arena&f: &f<arena_name>'
        - '&f• &7Kit&f: &f<kit_name>'
        - '&7'
        - '&f• &aApuesta&f: &f<bet_type>'
    ARENA:
      NAME: '&bArena'
      LORE:
        SENDER:
          - '&7'
          - '&f• &7Selección&f: &f<arena_name>'
          - ''
          - '&e¡Click para cambiar la Arena!'
        RECEIVER:
          - '&7'
          - '&f• &7Selección&f: &f<arena_name>'
          - ''
    KIT:
      NAME: '&bKit'
      LORE:
        SENDER:
          - ''
          - '&f• &7Kit&f: &f<kit_name>'
          - ''
          - '&e¡Click para cambiar el Kit!'
        RECEIVER:
          - ''
          - '&f• &7Kit&f: &f<kit_name>'
          - ''
    BET_TYPE:
      NONE:
        NAME: '&4❌ &cNinguna'
        LORE:
          SENDER:
            - ''
            - '&f• &7Selección&f: &f<bet_type>'
            - ''
            - '&e¡Click para cambiar el Kit!'
          RECEIVER:
            - ''
            - '&f• &7Selección&f: &f<bet_type>'
            - ''
      ITEMS:
        NAME: '&2✅ &aItems'
        LORE:
          SENDER:
            - ''
            - '&f• &7Selección&f: &f<bet_type>'
            - ''
            - '&e¡Click para cambiar el Kit!'
          RECEIVER:
            - ''
            - '&f• &7Selección&f: &f<bet_type>'
            - ''
      COINS:
        NAME: '&6🪙 &eCoins'
        LORE:
          SENDER:
            - ''
            - '&f• &7Selección&f: &f<bet_type>'
            - ''
            - '&e¡Click para cambiar el Kit!'
          RECEIVER:
            - ''
            - '&f• &7Selección&f: &f<bet_type>'
            - ''
    PLAYER:
      LORE:
        - ''
        - '&f• &7Jugador&f: &f<target_name>'
        - ''
    ACCEPT:
      NAME: '&2✅ &aAceptar &2✅'
      LORE:
        - ''
        - '&eClick para &aaceptar&e la invitacion.'
    DECLINE:
      NAME: '&4❌ &cRechazar &4❌'
      LORE:
        - ''
        - '&eClick para &crechazar&e la invitacion.'
    SEND:
      NAME: '&6🔥 &eEnviar &6🔥'
      LORE:
        - ''
        - '&eClick para &aenviar&e la invitacion.'
    SELECT_KIT_FIRST: "&cDebes de seleccionar un kit primero!"
KIT:
  MENU:
    RANDOM:
      NAME: '&bRandom'
      LORE:
        - ''
        - '&f• &7Usarás un &fKit Aleatorio.'
        - ''
        - '&e¡Click para seleccionar!'
    OWN_INVENTORY:
      NAME: '&bInventario propio'
      LORE:
        - ''
        - '&f• &7Usarás tu &fpropio inventario&7 como &fKit Base&7.'
        - ''
        - '&e¡Click para seleccionar!'
    EDIT:
      CREATE:
        NAME: '&2✅ &aCrear &7(Custom)'
        LORE:
          - ''
          - '&f• &7Crea un &aKit Propio&7, diseñado a tu gusto.'
          - '&a→ &7Nombre: &f<kit_name>'
          - ''
          - '&4❌ &c¡No puedes crear más de &4<kits_max> &4Kits!'
          - ''
          - '&e¡Click para crearlo!'
        MAX_KITS: '&cNo puedes crear mas de &f<kits_max> &cKits'
      DELETE:
        NAME: '&4❌ &cBorrar &4❌'
        LORE:
          - ''
          - '&f• &7Borrarás este kit para siempre.'
          - ''
          - '&e¡Click para borrar!'
      DEFAULT_INVENTORY:
        NAME: '&aInventario por defecto'
        LORE:
          - ''
          - '&f• &7Usarás el inventario por &fDefecto&7.'
          - ''
          - '&e¡Click para seleccionar!'
      SAVE:
        NAME: '&2✅ &aGuardar &2✅'
        LORE:
          - ''
          - '&f• &7Se guardarán los ítems que tienes en tu inventario.'
          - '&e¡Click para guardar los cambios!'
        SUCCESS: '&2✔ &aKit Guardado correctamente.'
      DEFAULT_ARMOR:
        NAME: '&aArmadura por defecto'
        LORE:
          - ''
          - '&f• &7Esta es la armadura que se usará cuando te equipes un kit.'
          - ''
          - '&c• &7No puede modificarse'
PARTY:
  DISABLED: '&c❌ Las parties están deshabilitadas.'
  PARTY_NOT_FOUND: '&c❌ No existe un grupo con ese ID!'
  PARTY_ALREADY_IN_MATCH: '&c❌ Este grupo ya está en un evento!'
  PARTY_LEAVE: '&e🚪 Has salido del grupo &a<party_name>&e!'
  PARTY_JOIN: '&e🎉 Has entrado al grupo &a<party_name>&e!'
  PARTY_INVITE: '&e📨 Has invitado a &a<player_name> &ea unirse al grupo &a<party_name>&e!'
  PARTY_ACCEPT: '&e✔ Has aceptado la invitación de &a<player_name> &epara unirse al
    grupo &a<party_name>&e!'
  PARTY_DECLINE: '&e❌ Has rechazado la invitación de &a<player_name> &epara unirse
    al grupo &a<party_name>&e!'
  PARTY_DISBAND: '&e🚫 Has disuelto el grupo &a<party_name>&e!'
  PARTY_KICK: '&e🔨 Has expulsado a &a<player_name> &edel grupo &a<party_name>&e!'
  SENT_INVITE: '&basd'
TAGS:
  MENU:
    TAG_SECTION:
      DISABLED: '&cEsta seccion esta deshabilitada'
      OWN_TAGS:
        NAME: '&eTus tags'
        LORE:
          - '&eClic para ver tus tags'
      DEFAULT:
        NAME: '&e<tag_section_name>'
        LORE:
          - '&eClic para seleccionar &b<tag_section_name>'
    TAG:
      GIVEN_TAG: '&eHaz regalado el tag &b<tag_name> &ea &f<user_name>'
      RECEIVED_TAG: '&eHaz recibido el tag &b<tag_name>'
      TAKEN_TAG: '&eLe haz quitado el tag &b<tag_name> &ea &f<user_name>'
      LOST_TAG: '&eEl tag &b<tag_name> &eha sido removido de tu cuenta.'
      NO_PERMISSION:
        - '&cNo tienes permisos para equiparte este tag.'
        - '&7Puedes comprar tags en &btienda.prismamc.net'
      SELECTED_TAG: '&aTe has equipado &b<tag_name>'
      UN_SELECTED_TAG: '&aTe has des-equipado &b<tag_name>'
      DEFAULT:
        EQUIPPED:
          NAME: '&e<tag_display_name>'
          LORE:
            - '&cClic para desequipar.'
        UNEQUIPPED:
          NAME: '&e<tag_display_name>'
          LORE:
            - '&ePrefix: &b<tag_prefix>'
            - ""
            - '&aClic para equipar.'
NIGHT_MARKET:
  NEXT_MESSAGE: "&eEl siguiente nightmarket aparecera en: &c<cooldown>"
  DESPAWN: "&cEl NightMarket se ha marchado!"
  SPAWN:
    - '&7&m-------------------------------'
    - '           &8&lNightMarket'
    - '&7Un mercado nocturno ha aparecido'
    - '&7Compra items al &emejor precio!'
    - ''
    - '&eCoordenadas: &c<location>'
    - '&eDuración: &c<duration_short>'
    - '&7&m-------------------------------'
ENDER_CHEST:
  BLOCKED_SLOT: "<red>No tienes acceso a esta sección del enderchest!"
  ITEM_NAME: "&cEste slot aun no está desbloqueado!"
  ITEM_LORE:
    - "&f"
    - "&b&l| &r&cAdquiere un rango en &b&ntienda.prismamc.net&c"
    - "&b&l| &r&cPara desbloquear este slot de manera &dinstantanea&c!"
NEWBIE_PROTECTION:
  ENABLED: "&a&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fHas recibido una &bprotección &fdurante &e1 hora!&f Por este tiempo no &cpodrás morir&f, pero tendrás ciertas limitaciones. Si quieres desactivarla puedes hacerlo con el comando &4/pvp enable"
  EXPIRED: "&4&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fTu protección al PVP ha &c&n&lEXPIRADO&r&c!&f Ten cuidado, &eya puedes &n&eMORIR&r&e."

  ACTIONBAR: "&a&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8&l|&r &e<duration_short>"

  MINE_BREAK_DISABLED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fNo puedes romper esta mina con tu &eprotección al PVP activa! &fSi deseas desactivarla usa el comando &4/pvp enable"
  KOTH_DISABLED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fNo puedes campear el &4&lKOTH&r&f con tu &eprotección al PVP activa! &fSi deseas desactivarla usa el comando &4/pvp enable"
  ABILITY_USE_DISABLED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fNo puedes usar &ditems especiales&f con tu &eprotección al PVP activa! &fSi deseas desactivarla usa el comando &4/pvp enable"
  TREASURE_CHEST_DISABLED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fNo puedes abrir este &dcofre del tesoro&f con tu &eprotección al PVP activa! &fSi deseas desactivarla usa el comando &4/pvp enable"
  MOVE_DISABLED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fNo puedes entrar a esta zona con tu &eprotección al PVP activa! &fSi deseas desactivarla usa el comando &4/pvp enable"
  PICKUP_DISABLED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fNo puedes recoger items con tu &eprotección al PVP activa! &fSi deseas desactivarla usa el comando &4/pvp enable"
  ATTACK_DISABLED: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fNo puedes &catacar otros jugadores&f con tu &eprotección al PVP activa! &fSi deseas desactivarla usa el comando &4/pvp enable"
  PLAYER_HAS_PROTECTION: "&c&lᴘʀᴏᴛᴇᴄᴄɪᴏɴ ᴘᴠᴘ&r &8➜ &fEste jugador tiene una &eprotección al PVP&f activa &c¡No puedes atacarlo!"
GG_WAGE:
  STARTED: "&aHa iniciado una ola de GGs! &eEscribe GG en el chat para ganar recompensas!"
  ENDED: "&cLa ola de GG ha terminado."
  WON: "&aHas recibido tus recompensas por escribir GG!"
DTC:
  STARTED:
    - "<prisma_font>&7&l&m----------------------------------------"
    - "<prisma_font>&fevento &c&ldtc&f ha iniciado! se el ultimo en destruir"
    - "<prisma_font>&fel nexo y gana recompensas increibles!"
    - ""
    - "<prisma_font>&ccoordenadas: &e<location>"
    - "<prisma_font>&7&l&m----------------------------------------"

  HITS_LEFT: "&c&lᴅᴛᴄ&r &8➔ &fLa &cvida del nexo &fha bajado a &a<hits>&f! &eSe el ultimo en romperlo para ganar recompensas!"
  WINNER: "&c&lᴅᴛᴄ&r &8➔ &fEl jugador &e<user_name> &aha ganado &fel evento &c&lDTC&r&c!"
AIRDROP:
  SPAWN: "&d&lᴀɪʀᴅʀᴏᴘ &8&l➜&r &fUn &e&nAirDrop&r &fva a caer en las coordenadas &a<location>"
COIN_FLIP:
  ERROR: "&cHa ocurrido un error con este &6Coinflip!"
  CANNOT_INVITE_SELF: "&cNo puedes iniciar un &6Coinflip &ccontigo mismo!"
  OPPONENT_OFFLINE: "&cHa ocurrido un error! Tu oponente no está conectado."
  NOT_MEET_REQUIREMENTS: "&cNo tienes los requisitos para entrar en esta apuesta!"
  NOT_ENOUGH_COINS_TO_CREATE: "&cNo tienes suficientes coins para crear este &6coinflip!"
  INVALID_COINS_AMOUNT: "&cNo puedes apostar cantidades menores a &61 coin!"
  INVALID_COINS_NUMBER: "&cTienes que ingresar un numero! Escribe 'cancelar' para detener."
  NOT_ENOUGH_COINS: "&cNo tienes suficientes &6coins &cpara entrar en esta apuesta!"
  NOT_ITEM_ADDED: "&cIngresa un item para aceptar!"
  ALREADY_CREATED: "&cYa tienes un &6Coinflip &cactivo!"
  NO_BET_FOUND: "&cNo se ha encontrado ninguna apuesta a tu nombre!"

  SEND_COINS_AMOUNT: "&aEnvia la cantidad de coins que quieres apostar por el &cchat!"

  CREATED_COINS: "&aHas creado un &6Coinflip &apor: &6<amount> coins!"
  CREATED_ITEMS: "&aHas creado un &6Coinflip!"

  CANCELLED: "&aTu &6apuesta &aha sido cancelada!"

  START_MESSAGE: "&aTu apuesta por <bet_string> va a comenzar! &eContrincante: <user_name>"
  WIN_MESSAGE: "&aHas ganado el &6coinflip! &aGanaste <bet_string>&a!"
  LOSE_MESSAGE: "&cHas perdido el &6coinflip! &cPerdiste <bet_string>&c!"

  WIN_BROADCAST: "&e<looser_name> &aha perdido <bet_string> &acontra &e<user_name> &aen un &6coinflip! &7( /cf )"
  MENUS:
    MAIN_MENU:
      INFO_ITEM:
        NAME: "&6&lCoinflip"
        LORE:
          - "&b▎ &fApuesta &eitems &fy &6coins &fcon jugadores"
          - "&b▎ &fContra otros &cjugadores!"
      COINS_ITEM:
        NAME: "&e&lCoins"
        LORE:
          - "&b▎ &fApuesta &6coins &fcontra otros"
          - "&b▎ &cjugadores"
      ITEMS_ITEM:
        NAME: "&c&lItems"
        LORE:
          - "&b▎ &fApuesta &eitems &fcontra otros"
          - "&b▎ &cjugadores"
    LIST_MENU:
      BET_ITEM:
        NAME: "&e<user_name>'s &6Coinflip"
        LORE:
          - "&b▎ &cApuesta: <bet_string>"
          - ""
          - "&b▎ &fHaz &cclick &fpara entrar"
          - "&b▎ &fen este &6Coinflip!"
      CREATE_ITEM:
        NAME: "&a&lCrear Coinflip"
        LORE:
          - "&b▎ &fHaz &cclick &fpara crear"
          - "&b▎ &fUn &6Coinflip!"
SERVER:
  ONLINE_STATUS: "&aActivo"
  OFFLINE_STATUS: "&cApagado"
  WHITELISTED_STATUS: "&eRestringido"
  INITIALIZING_STATUS: "&eIniciando &7( <percentage>% )"
REGION_EVENTS:
  NOT_FOUND: '&cNo se ha encontrado ninguna region con ese nombre!'
  CREATE:
    CREATED: '&aHas creado una region de eventos!'
    NO_REGION_SELECTED: '&cDebes de seleccionar una region primero!'
  EVENT:
    START:
      - '&7--------------------------------------------'
      - ''
      - '&7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &5&lCasa Embrujada'
      - ''
      - '&7 &7 &7 &fEl evento &e<event_name> &fha comenzado!'
      - ''
      - '&7<event_description>'
      - ''
      - '&7--------------------------------------------'
    FINISH:
      - '&7--------------------------------------------'
      - ''
      - '&7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &5&lCasa Embrujada'
      - ''
      - '&7 &7 &7 &7 &7 &7 &7 &cEl evento &e<event_name>&r &cha finalizado.'
      - ''
      - '&7--------------------------------------------'
    NO_ELYTRAS:
      DISPLAY_NAME: '&cElytras Bloqueadas'
      DESCRIPTION:
        - '&7 &7 &7 &7Las elytras serán &cbloqueadas &7durante el evento!'
      ELYTRAS_DISABLED: '&cNo puedes usar elytras en este evento!'
    FFA:
      START_TITTLE: '&4&lFFA'
      START_SUBTITLE: '&cSe el último en pie!'
      PLAYER_DIE: '&c&lEl jugador <user_name> ha sido eliminado! &7( quedan <remaining_players>
        jugadores )'
      WINNER: '&cEl jugador &e<user_name> &cha ganado el evento!'
      DISPLAY_NAME: '&c&lFFA&r'
      DESCRIPTION:
        - '&7 &7 &7Inicia una pelea a muerte con un kit custom'
        - '&7 &7 &5Contra todos los jugadores&7 en la casa y se'
        - '&7 &7 &eEl ultimo en pie para recibir recompensas &e&lOP&r!'
        - '&8 &7 &r&c&nNo perderas nada de tu inventario si mueres&r&c.'
      CANNOT_ENTER: '&cEl evento de FFA está en progreso! No puedes ingresar.'
      CANNOT_LEAVE: '&cEl evento de FFA está en progreso! Solo un jugador podrá salir
        con vida.'
    COINS_RAIN:
      DISPLAY_NAME: '&eLluvia de Coins'
      DESCRIPTION:
        - '&7 &7 Recoge la mayor cantidad de &6coins &7que puedas!'
        - '&7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &7 &e&nCaen del cielo&r&e!'
      COINS_PICKUP: '&aHas recogido &6<coins> coins&a!'
  COMMAND_BLACKLISTED: '&cNo puedes usar ese comando en esta zona!'
DEATHS:
  NO_PERMISSION_TO_RESTORE: '&3&lPrisma&f&lMC&r &7»&r &c¡No tienes permisos para restaurar
    inventarios!'
  INVENTORY_RESTORED: '&3&lPrisma&f&lMC&r &7»&r &a¡Has restaurado correctamente el
    inventario de <user_name>!'
  DEATHS_MENU_LORE:
    - ''
    - ' &eAsesino: &c<killer_name>'
    - ' &eMensaje: &c<message>'
    - ' &eMundo: &c<world>'
    - ''
    - '&e¡Clic aquí para gestionar esta muerte!'
    - ''
  INVENTORY_NOT_EMPTY: '&cEl inventario del jugador no está vacio, estás seguro que
    quieres sobreescribirlo? Haz click otra vez para confirmar.'
  INVENTORY_CONTAINS_LEGENDARY_PICKAXE: "&cEl inventario del jugador contiene un pico legendario. No se puede reestablecer!"
  ALREADY_RESTORED: '&cEste inventario ya fue restaurado!'
SYNC:
  ALREADY_SYNCED: "<red>Your account is already synced with Discord!"
  ERROR: "<red>An error occurred while trying to sync your account. Please try again later."
  GENERATE_CODE: "<primary>Your sync code is: <secondary><code><primary>. Use this code in Discord to link your accounts!"
  NOT_SYNCED: "<red>Your account is not synced with Discord. Use <secondary>/sync<red> to link your accounts."
  REMOVE: "<primary>Your Discord sync has been successfully removed!"
  SUCCESS: "<primary> Your Discord sync has been successfully <green>sync<primary>!"
  REMOVE_OTHERS_NO_PERMISSION: "<red>No permission."
OUTFITS:
  REMOVE: "&cHas eliminado el outfit &b<outfit_name>&c!"
  UPDATED_WITH_INVENTORY: "&5&lOutfits&r &8➜ &aOutfit actualizado correctamente!"
  LIMIT_REACHED: "&cHas alcanzado el limite de outfits que puedes tener! Adquiere un rango en &btienda.prismamc.net &cpara aumentar el limite."
  RENAME_PROMPT:
    MESSAGE: "&5&lOutfits&r &8➜ &fEscribe el nuevo nombre del outfit &8( cancelar para salir )"
    INVALID_NAME: "&5&lOutfits&r &8➜ &cEl nombre del outfit no es valido!"
  AMOUNT_PROMPT:
    MESSAGE: "&5&lOutfits&r &8➜ &fEscribe la nueva cantidad que quieras asignar al item &8( cancelar para salir )"
    INVALID_INPUT: "&5&lOutfits&r &8➜ &cLa cantidad no es valida! Ingresa un número del 1 al 64 &8( cancelar para salir )"
  APPLY:
    COOLDOWN: "&5&lOutfits&r &8➜ &cNo puedes aplicar el outfit tan seguido! Espera &e<duration_full>&c."
    INVENTORY_NOT_EMPTY_CONFIRM: "&5&lOutfits&r &8➜ &cTu inventario no está vacio, el outfit no se aplicará en los slots usados. Equipa el outfit nuevamente para continuar de todos modos."
    SUCCESS_FULLY: "&5&lOutfits&r &8➜ &aOutfit aplicado correctamente!"
    SUCCESS_EMPTY: "&5&lOutfits&r &8➜ &cNo se ha aplicado ningún item del outfit."
    SUCCESS_PARTIALLY:
      - "&5&lOutfits&r &8➜ &fEl outfit ha sido aplicado con objetos faltantes!"
      - "&5&lOutfits&r &8➜ &c<missing_items>"
    FAILURE: "&5&lOutfits&r &8➜ &cNo se ha podido aplicar el outfit!"
ANNIVERSARY:
  CLAIMING: '&d&lAniversario&r &8➜ &fReclamando mision....'
  DAY_CLAIMED: '&d&lAniversario&r &8➜ &a¡Reclamado la mision diaria!'
  MAX_ALTS_REACHED: '&d&lAniversario&r &8➜ &c¡Has alcanzado el máximo de cuentas alternas que puedes reclamar la recompenza de este día!'
  MISSIONS:
    COMPLETED: '&d&lAniversario&r &8➜ &a¡Has completado la misión diaria!'
    CAKE_FOUND: '&d&lAniversario&r &8➜ &f¡Has encontrado una &d🎂 tarta&f! &7(&d<cakes>&7/&f100&7)'
    GLOBAL:
      CLAIMED_BROADCAST: '&d&lAniversario&r &8➜ &f¡El jugador &b<user_name> &fha completado
        la &dmisión global de aniversario&f y ha recibido el rango &5&lANIVERSARIO&f!'
      CLAIMED: '&d&lAniversario&r &8➜ &a¡Has reclamado tu recompensa global!'
      ALREADY_CLAIMED: '&d&lAniversario&r &8➜ &cYa has reclamado la misión global.'
      NOT_COMPLETED: '&d&lAniversario&r &8➜ &cAún no has completado la misión global:
        &fHas encontrado &d<cakes> &f/ &d100 &ftartas.'
ANTI_DUPE:
  DUPED_ITEM: "&c&lᴀɴᴛɪ-ᴅᴜᴘᴇ&r &8➜ &fSospechamos que este item ha sido duplicado! &cPor favor, si crees que esto es un error, crea un ticket en: &bdiscord.gg/prismamc"
  DUPED_ITEM_BYPASS: "&c&lᴀɴᴛɪ-ᴅᴜᴘᴇ&r &8➜ &fEstás moviendo un item duplicado... &7( bypass )"
  SHULKER_BYPASS: "&c&lᴀɴᴛɪ-ᴅᴜᴘᴇ&r &8➜ &fAbriendo shulker dupeada... &7( bypass )"

TRADE:
  ITEMS:
    COIN:
      NAME: "&6✦ Gold Coin ✦"
      LORE:
        - "&7A tradable currency token."
        - "&7Value: &e<amount> coins"
        - "&8Use in secure trades only."
  MENU:
    LOADING_BUTTON:
      DISPLAY_NAME: "&6&lLoading..."
      LORE:
        - "&7Fetching trade history from database..."
        - "&7Please wait a moment."

    NO_TRADE_HISTORY_BUTTON:
      DISPLAY_NAME: "&c&lNo Trade History"
      LORE:
        - "&7This player has no recorded trades."
        - "&7Trades will appear here once completed."

    REFRESH_BUTTON:
      DISPLAY_NAME: "&a&lRefresh"
      LORE:
        - "&7Click to refresh the trade history"
        - "&7and load any new trades."

    TRADE_HISTORY_BUTTON:
      DISPLAY_NAME: "&6&lTrade with <other>"
      LORE:
        - "&7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        - "&7Traded with: &f<other>"
        - "&7Date: &e<date>"
        - ""
        - "&7Your Items: &f<viewer_items>"
        - "&7Their Items: &f<other_items>"
        - ""
        - "&6&lStatus: &a&lCompleted"
        - ""
        - "&7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        - "&e&lClick to view detailed trade information!"
      GLOW: true

    TRADE_VIEW:
      BACK_BUTTON:
        DISPLAY_NAME: "&c&lBack"
        LORE:
          - "&7Click to return to the"
          - "&7trade history menu."

      INFO_BUTTON:
        DISPLAY_NAME: "&6&lTrade Information"
        LORE:
          - "&7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          - "&6&lTrade Information:"
          - ""
          - "&7Player 1: &f<viewer>"
          - "&7Player 2: &f<other>"
          - ""
          - "&a&lItems Given by <viewer>: &7(<viewer_items>)"
          - "&c&lItems Given by <other>: &7(<other_items>)"
          - ""
          - "&6&lStatus: &a&lCompleted Successfully"
          - "&7━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        GLOW: true

    COIN_TRADE_BUTTON:
      DISPLAY_NAME: "&6Trade coins"
      LORE:
        - "&7Click to trade your coins"


    CONFIRMATION_STATUS_BUTTON:
      CONFIRMED:
        DISPLAY_NAME: "&a&lConfirmed!"
        LORE:
          - "&7<other> has accepted the trade."
      PENDING:
        DISPLAY_NAME: "&e&lWaiting for <other>"
        LORE:
          - "&7Waiting for <other> to confirm..."

    INPUT_BUTTON:
      CONFIRMED:
        DISPLAY_NAME: "&aDeal accepted!"
        LORE:
          - "&7You are still waiting for the"
          - "&7other side to accept"
      COUNTDOWN:
        DISPLAY_NAME: "&cWait <countdown> seconds"
        LORE:
          - "&7Please check your items before accepting"
          - "&7You must wait before confirming"
          - "&7the trade after changes"
      EMPTY:
        DISPLAY_NAME: "&aTrading"
        LORE:
          - "&7You need to click an item in your"
          - "&7inventory to offer it for trade"
      GIFT:
        DISPLAY_NAME: "&bGift!"
        LORE:
          - "&7You are receiving items without"
          - "&7giving anything in return"
      WARNING:
        DISPLAY_NAME: "&eWarning!"
        LORE:
          - "&7You are offering items without"
          - "&7getting anything in return"

  MESSAGES:
    FAILED_NO_SPACE: "&cTrade failed: someone needs more empty slots!"
    TRADE_CANCELLED: "&cTrade cancelled."
    TRADE_COMPLETED: "&a&l✓ Trade completed successfully!"
    ITEM_GIVEN_FORMAT: "&c- &fx<amount> &7<item>"
    ITEM_RECEIVED_FORMAT: "&a+ &fx<amount> &7<item>"
    USAGE: "&eUsage: /trade <player>"
    SELF: "&cYou cannot trade with yourself."
    REQUEST_SENT: "&aYou sent a trade request to <target>"
    REQUEST_RECEIVED: "&e<sender> has sent you a trade request. Use /trade accept or /trade decline."
    REQUEST_FAILED: "&cTrade request could not be sent. Maybe the target already has a pending request."
    REQUEST_ACCEPTED_BY_TARGET: "&aYou accepted a trading request from <player>"
    REQUEST_ACCEPTED: "&a<player> accepted your trade request."
    NO_PENDING_REQUEST: "&cYou don't have any pending trade request."
    REQUEST_DECLINED: "&cYou declined the trade request."
    ITEMS_FULL: "&cYou cannot add more items."
    INVENTORY_FULL: "&cYour inventory is full."
    IGNORE_ON: "&aYou are now ignoring trade requests."
    IGNORE_OFF: "&cYou are no longer ignoring trade requests."
    TARGET_IGNORING: "&cThat player is ignoring trade requests."
    PROFILE_NOT_FOUND: "&cProfile not found. Please relog or contact an admin."
    INVALID_AMOUNT: "&cInvalid amount entered."
    NO_ACTIVE_TRADE: "&cYou are not currently in a trade."


    COIN_NOT_ENOUGH: "&cYou do not have enough coins."
    COIN_INPUT_PROMPT: "&eEnter the amount of coins to add, or type &ccancel&e to cancel."
    COIN_INPUT_CANCELLED: "&cCoin input cancelled."
    COIN_INPUT_INVALID_AMOUNT: "&cPlease enter a positive number."
    COIN_INPUT_NOT_NUMBER: "&cInvalid number. Please enter a valid amount."
    COIN_INPUT_SUCCESS: "&aAdded &e<coins> &acoins to the trade."
    COIN_ADDED: "&aAdded &e<amount> coins &ato your trade."

    HOVER_ACCEPT: "&a✔ Click to accept <sender>'s trade request"
    HOVER_DECLINE: "&c✖ Click to decline <sender>'s trade request"
    HOVER_ACCEPT_HOVER: "&7Accept <sender>'s trade request."
    HOVER_DECLINE_HOVER: "&7Decline <sender>'s trade request."