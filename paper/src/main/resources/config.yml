# Prisma Core Main Configuration

# Connection Settings
connection:
  mongo:
    address: localhost
    port: 27017
    database: "prisma-core"
    authenticate: false
    username: ""
    password: ""
    authDatabase: "admin"
  redis:
    address: localhost
    port: 6379
    databaseId: 0
    authenticate: false
    useUsername: false
    username: ""
    password: ""
  tebex:
    secret: "TBX-SECRET"

# Server Settings
server:
  colors:
    primary: "<yellow>" # Primary color used in server messages
    secondary: "<green>" # Secondary color used in server messages
  data:
    name: "Prisma" # Server name
    type: LOBBY # Server type (PROXY, LOBBY, BOXPVP, GENS, MINIGAMES, PRACTICE)
    old_database_name: "" # Old server profiles collection prefix (rush, simpson) - on db: prefix_profiles

# Custom Systems and Fixes Settings
systems:
  # Give players specific items on their first join
  FIRST_JOIN_ITEMS:
    ENABLED: true
  # Fix for absorption hearts not reapplying if the player still has the absorption effect; removes the effect when hearts are depleted
  ABSORPTION_FIX:
    ENABLED: true
  # Deprecated: Old block placement system (previous system used in Mario Season 1, <PERSON>, and Toybox)
  OLD_BLOCK_PLACEMENT:
    ENABLED: false
  # Prevents blocks like sand and gravel from falling
  DISABLE_FALLING_BLOCKS:
    ENABLED: true
  # Grants permanent night vision to all players; cannot be removed
  NIGHT_VISION:
    ENABLED: true
  # To do: remove
  REAL_MINES_ARMOR_DROP:
    ENABLED: true
  # Fix for allowing Java and Bedrock players to escape cobwebs using fireworks and elytras
  FIREWORK_GLITCH_FIX:
    JAVA_FIX: true # Enable fix for Java players
    BEDROCK_FIX: true # Enable fix for Bedrock players
  # New block placement system
  BLOCK_PLACEMENT:
    ENABLED: true
    WARZONE_REGION_NAME: warzone # Name of the Warzone region where specific blocks can be placed
    ALLOWED_BLOCKS:
      - "COBWEB" # List of blocks allowed to be placed
      - "LIME_WOOL"
    WARZONE_DURATION: 30 # Duration in seconds that blocks remain in Warzone
    PLACEABLE_ON_MINES: true # Whether blocks can be placed in mine regions
    MINE_DURATION: 15 # Duration in seconds that blocks remain in mines
  # Allows specific items to have infinite use based on permissions, regions, and allowed worlds.
  INFINITE_ITEMS:
    ENABLED: true
    ITEMS:
      GOLDEN_APPLE: # Item material name
        PERMISSION: "core.infinite.golden_apple" # Permission required for infinite golden apples.
        REGIONS: [] # List of regions where the item is infinite without having the permission.
        ALLOWED_WORLDS:
          - "world" # List of worlds where the item can be infinite (all for all worlds).
      FIREWORK_ROCKET:
        PERMISSION: "core.infinite.firework"
        REGIONS:
          - "spawn"
        ALLOWED_WORLDS:
          - "world"
  ANTI_KILL_BOOSTING:
    ENABLED: false
    MIN_ARMOR_PRIORITY: 20
    NO_KILLS_WORLDS:
      - "duels"
  CLANS_NERF:
    ENABLED: false
    MAX_NEARBY_CLAN_MEMBERS: 5
    CLAN_MEMBERS_RANGE: 10
    CLANS_NEARBY_RANGE: 15
    NERF_DAMAGE_REDUCE_PERCENTAGE: 20
    NERF_RESISTANCE_REDUCE_PERCENTAGE: 10
  PLAYER_ATTRIBUTES:
    ENABLED: true
    HIDDEN_MODE: false # If enabled, reach and aim assist will not work when the target entity is still
    AIM_ASSIST_INCREMENT: 1 # Represents the distance between each raytrace when using the aim assist attribute

    # Anti-gank mode
    ANTIGANK_DAMAGE_PROTECTION_PERCENTAGE: 20 # The percentage of damage reduction the player will receive when the antigank mode is active
    ANTIGANK_DURATION: 20s # The duration of the anti-gank mode when triggered
    REQUIRED_NEARBY_PLAYERS_TO_TRIGGER_ANTIGANK: 4 # The minimum amount of nearby players (from a different clan) required to trigger anti-gank mode
    REQUIRED_NEARBY_PLAYERS_SEARCH_RANGE: 8 # The range (in blocks) in which 'nearby players' will be searched
    REQUIRED_DAMAGE_TO_TRIGGER_ANTIGANK: 30 # Damage taken required to trigger the anti-gank mode
    DAMAGE_TIME_FILTER_ANTIGANK: 10s # Time filter for REQUIRED_DAMAGE_TO_TRIGGER_ANTIGANK
  CUSTOM_EFFECTS:
    ENABLED: true
    STRENGTH_DAMAGE_MULTIPLIER: 5
    WEAKNESS_REDUCE_PER_LEVEL: 10
  INVENTORY_REMOVER:
    ENABLED: true
    ITEMS:
      TEST:
        TYPE: "contains"
        NAME: "hola"
  INVENTORY_SPOOFER:
    ENABLED: true
    EQUIPMENT_SPOOF_AMOUNT: 1
  BATTLE_PASS:
    ENABLED: true
    PRESTIGE_RANGE: 100
    PRESTIGE_LEVELS: 4
    DAILY_MISSIONS:
      KILL_THREE:
        TYPE: KILL
        KILLS_REQUIRED: 3
        DESCRIPTION: "&eAsesina 3 jugadores."
        REWARD:
          DISPLAY:
            - "&a+5 Prestigios"
          REWARDS:
            PRESTIGE:
              COMMAND:
                - "battlepass manage prestige add <player_name> 5"
    REWARDS:
      1:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      2:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      3:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      4:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      5:
        TYPE: OP
        COST: 1
        DISPLAY:
          - "x5 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 5
      6:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      7:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      8:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      9:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      10:
        TYPE: OP
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      11:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      12:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
      13:
        TYPE: NORMAL
        COST: 1
        DISPLAY:
          - "x1 Golden Apple"
        REWARDS:
          APPLE:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1
  NIGHT_MARKET:
    ENABLED: false
    SPAWN_DELAY: 2h
    DURATION: 8m
    ITEMS:
      '0':
        MATERIAL: GOLDEN_APPLE
        MIN_PRICE: 5
        MAX_PRICE: 30
        ALWAYS_SPAWN: true
  SOTW:
    ENABLED: true
  ITEM_DISABLER:
    ENABLED: true
    DISABLE_CRAFT: true
    DISABLED_ITEMS:
      - "ENDER_EYE"
      - "END_CRYSTAL"
  AIRDROP:
    ENABLED: false
    DURATION: 30s
    SPAWN_COOLDOWN: 30m
    LOOT:
      '0':
        ABILITY: MAGE
        AMOUNT: 8
      '1':
        ABILITY: RAGE
        AMOUNT: 5
      '2':
        ABILITY: HEALER
        AMOUNT: 5
      '3':
        ABILITY: ELYTRA_DISABLER
        AMOUNT: 5
      '4':
        ABILITY: LIFE_STEALER
        AMOUNT: 5
      '5':
        MATERIAL: ENCHANTED_GOLDEN_APPLE
        AMOUNT: 16
      '6':
        MATERIAL: GOLDEN_APPLE
        AMOUNT: 64
      '7':
        MATERIAL: GOLDEN_APPLE
        AMOUNT: 32
      '8':
        MATERIAL: LIME_WOOL
        AMOUNT: 32
      '9':
        MATERIAL: COBWEB
        AMOUNT: 32
  NEWBIE_PROTECTION:
    ENABLED: true
    DURATION: 1h
    DISABLED_ARMORS:
      - "Mario"
    DISABLED_REGIONS:
      - "mario"
    DISABLED_TREASURE_CHESTS:
      - "EPIC"
      - "LEGENDARY"
  DTC:
    ENABLED: true
    TYPE: CREEPER
    REQUIRED_HITS: 500
    HITS_ANNOUNCE_INTERVAL: 50
    SPAWN_COOLDOWN: 1h
    REWARDS:
      - "cr key give <player_name> kits_key 1"
  REGION_EVENTS:
    ENABLED: true
  ANTI_PHASE:
    ENABLED: true
    ANTI_DOOR_PHASE: true
  FUNNY_ITEMS:
    ENABLED: true
    FUNNY_ITEM: CAKE
    APPLY_FUNNY_ITEM: false
  ANNIVERSARY:
    ENABLED: true
    START_DATE: "04/06/2025"
    DAYS:
      0:
        MISSION:
          ID: "anniversary_mission_0"
          TYPE: "FIND_BLOCK"
          LOCATION: 0,100,0
        REWARDS:
          0:
            MATERIAL: GOLDEN_APPLE
            AMOUNT: 1


# Service Settings
services_settings:
  ON_JOIN:
    SEND_TO_SPAWN: true # Teleport players to spawn on join
    SEND_TITLES: true # Send title messages to players on join
  STAFF:
    ALLOW-STAFF-MODE: true # Allow staff members to enter staff mode
    ALLOW-VANISH: true # Allow staff members to vanish from view
  COMBAT-TAG:
    ENABLED: true # Enable combat tagging system
    TIME: 8s # Duration of combat tag in seconds
    BLACKLISTED_WORLDS:
      - "duels"
  SCOREBOARD:
    ENABLED: true # Enable the server scoreboard
  CUSTOM-DROPS:
    ENABLED: true # Enable custom item drops
    TYPE: OLD # NEW, OLD
    DATABASE: "customdrops"
  TOMBSTONES:
    ENABLED: false # Enable tombstone system (when a player dies, a tombstone is created)
    TIME-TO-STEAL: 60 # Time in seconds before others can loot the tombstone
    TIME-TO-DESTROY: 120 # Time in seconds before the tombstone is destroyed
  CUSTOM-ARMOR:
    ENABLED: false # Enable custom armor system.
    ARMOR_REGIONS:
      Esqueleto: esqueleto
  STAFF-EVIDENCES:
    ENABLED: false
  ANTI-DUPE:
    ENABLED: false
  VOTE:
    COMMANDS:
      - "" # Commands to execute when a player votes
  SPARK_MONITOR:
    ENABLED: true
    DATA_WEBHOOK: ""
  WARPS:
    ENABLED: true # Enable the warp system
    MENU_ROWS: 3 # Warps menu rows
    DATABASE: ""
  TUTORIAL:
    TYPE: None # Tutorial mode (None, BoxPvP)
  SECURITY:
    ENABLED: true
    ENABLED_2FA: true
  EVENTS:
    ENABLED: true
    START_EVERY: 1h
  MINES_COMMAND:
    ENABLED: true
    TELEPORT_COOLDOWN: 15s
    RESET_COOLDOWN: 1h
    MINES:
      - "Luigi"
      - "Peach"
      - "Daisy"
      - "Goomba"
      - "Yoshi"
      - "Boo"
      - "Waluigi"
      - "Wario"
      - "Koopa"
      - "Toad"
      - "Dkong"
      - "Bowser"
      - "Mario"
  ECONOMY:
    DROP_MONEY_ON_DEATH: true
    MONEY_DROP_PERCENTAGE: 5
  CHAT:
    CHAT_FILTERED_WORDS:
      - "nigger"
  HIDDEN_PLAYERS:
    - "izLoki"
    - "6k2"
  QUEUE:
    GROUPS:
      owner:
        permission: "queue.owner"
        priority: 14
        bypass: true
      bypass:
        permission: "queue.bypass"
        priority: 13
        bypass: true
      staff:
        permission: "queue.staff"
        priority: 12
        bypass: false
      prisma:
        permission: "queue.prisma"
        priority: 10
        bypass: false
      amethyst:
        permission: "queue.amethyst"
        priority: 8
        bypass: false
      emerald:
        permission: "queue.emerald"
        priority: 6
        bypass: false
      sapphire:
        permission: "queue.sapphire"
        priority: 4
        bypass: false
      ruby:
        permission: "queue.ruby"
        priority: 2
        bypass: false
      amber:
        permission: "queue.amber"
        priority: 1
        bypass: false
  DEATH_MESSAGES:
    ENABLED: true
    SEND_WHEN_CHAT_MUTED: true
  ABILITIES:
    GLOBAL_COOLDOWN: 4s
  DISGUISE:
    COOLDOWN: 30m
  CLIENTS:
    TEAM_VIEW_ENABLED: false
  ENDER_CHEST:
    ENABLED: true
    SECTIONS:
      one:
        start: 27
        end: 31
        permission: enderchest.one
        material: RED_STAINED_GLASS_PANE
      two:
        start: 32
        end: 35
        permission: enderchest.two
        material: ORANGE_STAINED_GLASS_PANE
      three:
        start: 36
        end: 40
        permission: enderchest.three
        material: BROWN_STAINED_GLASS_PANE
      four:
        start: 41
        end: 44
        permission: enderchest.four
        material: YELLOW_STAINED_GLASS_PANE
      five:
        start: 45
        end: 49
        permission: enderchest.five
        material: GREEN_STAINED_GLASS_PANE
      six:
        start: 50
        end: 53
        permission: enderchest.six
        material: LIME_STAINED_GLASS_PANE
  JOIN_ME:
    ENABLED: true
  CANCEL_SUFFOCATION: true # Prevent players from suffocating in blocks
  TRADE_DROP_ITEMS: true

# Tips and Messages
TIPS:
  TIME: 180 # Interval time in seconds between showing tips
  LIST:
    '1':
      SPANISH:
        - ""
        - "&b&l                ¿SERVIDOR LLENO?"
        - ""
        - "&f ¿No puedes entrar cuando los servidores están llenos?"
        - "&f No te preocupes, adquiere un rango en nuestra tienda"
        - "&f         para poder entrar cuando quieras!"
        - ""
        - "&f       Compralo ahora en: &estore.prismamc.net"
        - ""
      ENGLISH:
        - ""
        - "&b&l                  SERVER IS FULL?"
        - ""
        - "&f       You can't join because servers are full?"
        - "&f       Don't worry, you can buy a rank on our store"
        - "&f       to join whenever you want even if servers are full!"
        - ""
        - "&f       Buy a rank now on: &estore.prismamc.net"
        - ""
    '2':
      SPANISH:
        - ""
        - "&9&l                 ¡NUESTRO DISCORD!"
        - ""
        - "&f    ¿Sabias que tenemos un servidor de &9&lDISCORD&f?"
        - "&f   En este podrás enterarte de todo lo que sucede en AstroGames."
        - ""
        - "&f       Únete usando: &prismamc.net/discord"
        - ""
      ENGLISH:
        - ""
        - "&9&l               OUR DISCORD SERVER!"
        - ""
        - "&f    Did you know that we have a &9&lDISCORD&f?"
        - "&f    Here you can stay updated on everything happening in AstroGames."
        - ""
        - "&f       Join now using: &prismamc.net/discord"
        - ""