package org.contrum.prisma;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.InvalidCommandArgument;
import co.aikar.commands.PaperCommandManager;
import com.github.retrooper.packetevents.PacketEvents;
import com.github.retrooper.packetevents.PacketEventsAPI;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sk89q.worldedit.regions.CuboidRegion;
import com.sk89q.worldedit.util.gson.VectorAdapter;
import joserodpt.realmines.api.RealMinesAPI;
import lombok.Getter;
import me.ulrich.clans.Clans;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.PluginManager;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.util.Vector;
import org.contrum.chorpu.menu.MenuManager;
import org.contrum.prisma.actionbar.ActionBarService;
import org.contrum.prisma.actionbar.bars.impl.placeholder.GlobalPlaceHolderActionBar;
import org.contrum.prisma.addon.AddonService;
import org.contrum.prisma.cacheService.CacheService;
import org.contrum.prisma.chat.ChatService;
import org.contrum.prisma.clients.ClientsService;
import org.contrum.prisma.coinflip.CoinFlipService;
import org.contrum.prisma.combatlogger.CombatLoggerService;
import org.contrum.prisma.commands.PrismaCoreCommand;
import org.contrum.prisma.commands.admin.*;
import org.contrum.prisma.commands.admin.customitems.CustomItemsCommand;
import org.contrum.prisma.commands.admin.customitems.items.LegendaryPickaxeCommand;
import org.contrum.prisma.commands.admin.discord.SendPMToDiscordCommand;
import org.contrum.prisma.commands.admin.evidences.PendingEvidencesCommand;
import org.contrum.prisma.commands.admin.evidences.PunishmentLogCommand;
import org.contrum.prisma.commands.admin.gamemode.CreativeCommand;
import org.contrum.prisma.commands.admin.gamemode.GamemodeCommand;
import org.contrum.prisma.commands.admin.gamemode.SpectatorCommand;
import org.contrum.prisma.commands.admin.gamemode.SurvivalCommand;
import org.contrum.prisma.commands.admin.grant.GrantCommand;
import org.contrum.prisma.commands.admin.grant.GrantsCommand;
import org.contrum.prisma.commands.admin.item.*;
import org.contrum.prisma.commands.admin.itemsearch.ItemSearchCommand;
import org.contrum.prisma.commands.admin.koth.KothBaseCommand;
import org.contrum.prisma.commands.admin.koth.PermanentKothCommand;
import org.contrum.prisma.commands.admin.koth.ScheduledKothCommand;
import org.contrum.prisma.commands.admin.rank.RankCommands;
import org.contrum.prisma.commands.admin.regions.RegionsCommand;
import org.contrum.prisma.commands.admin.regions.witchhouse.WitchHouseCommand;
import org.contrum.prisma.commands.admin.server.ServerManagerCommand;
import org.contrum.prisma.commands.admin.tag.TagCommand;
import org.contrum.prisma.commands.admin.tag.TagSectionCommand;
import org.contrum.prisma.commands.admin.timer.TimerCommand;
import org.contrum.prisma.commands.staff.*;
import org.contrum.prisma.commands.staff.alts.AltsCommand;
import org.contrum.prisma.commands.staff.chat.*;
import org.contrum.prisma.commands.staff.disguise.DisguiseCommand;
import org.contrum.prisma.commands.staff.disguise.DisguiseLogsCommand;
import org.contrum.prisma.commands.staff.disguise.DisguisePlayersCommand;
import org.contrum.prisma.commands.staff.economy.EconomyCommands;
import org.contrum.prisma.commands.staff.evidences.EvidencesCommand;
import org.contrum.prisma.commands.staff.invsee.InvseeCommand;
import org.contrum.prisma.commands.staff.logs.DeathsCommand;
import org.contrum.prisma.commands.staff.punishment.check.CheckCommand;
import org.contrum.prisma.commands.staff.punishment.commands.punish.*;
import org.contrum.prisma.commands.staff.punishment.commands.unpunish.UnBlackListCommand;
import org.contrum.prisma.commands.staff.punishment.commands.unpunish.UnMuteCommand;
import org.contrum.prisma.commands.staff.punishment.commands.unpunish.UnbanCommand;
import org.contrum.prisma.commands.staff.teleport.*;
import org.contrum.prisma.commands.user.*;
import org.contrum.prisma.commands.user.abilities.AbilitiesCommand;
import org.contrum.prisma.commands.user.economy.BalTopCommand;
import org.contrum.prisma.commands.user.economy.BalanceCommand;
import org.contrum.prisma.commands.user.economy.PayCommand;
import org.contrum.prisma.commands.user.enderchest.EnderchestCommand;
import org.contrum.prisma.commands.user.joinme.JoinMeCommand;
import org.contrum.prisma.commands.user.media.NewVideoCommand;
import org.contrum.prisma.commands.user.media.StreamCommand;
import org.contrum.prisma.commands.user.messaging.IgnoreCommand;
import org.contrum.prisma.commands.user.messaging.PrivateMessageCommand;
import org.contrum.prisma.commands.user.messaging.ReplyCommand;
import org.contrum.prisma.commands.user.messaging.TogglePrivateMessagesCommand;
import org.contrum.prisma.commands.user.mines.MinesCommand;
import org.contrum.prisma.commands.user.outfit.OutfitCommand;
import org.contrum.prisma.commands.user.reviveall.ReviveCommand;
import org.contrum.prisma.commands.user.reviveall.ThankCommand;
import org.contrum.prisma.commands.user.rocketspeed.RocketSpeedCommand;
import org.contrum.prisma.commands.user.social.*;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.customitems.CustomItemsService;
import org.contrum.prisma.customnpcs.CustomNPCService;
import org.contrum.prisma.customnpcs.npcs.seller.SellerItem;
import org.contrum.prisma.dailystreak.DailyStreakManager;
import org.contrum.prisma.dailystreak.command.RewardsCommand;
import org.contrum.prisma.deathmessage.DeathMessageService;
import org.contrum.prisma.disguise.DisguiseService;
import org.contrum.prisma.dropmultiplier.DropMultiplierService;
import org.contrum.prisma.dropmultiplier.DropMultiplierTimer;
import org.contrum.prisma.drops.blockdrops.BlockDropsService;
import org.contrum.prisma.drops.coindrops.CoinDropService;
import org.contrum.prisma.drops.customdrops.CustomDropService;
import org.contrum.prisma.drops.customdrops.command.CustomDropCommand;
import org.contrum.prisma.economy.EconomyService;
import org.contrum.prisma.enderchest.EnderChestService;
import org.contrum.prisma.essence.EssenceTraderService;
import org.contrum.prisma.gameevents.Event;
import org.contrum.prisma.gameevents.Events;
import org.contrum.prisma.gameevents.GameEventsService;
import org.contrum.prisma.giveaway.GiveAwayHandler;
import org.contrum.prisma.giveaway.commands.GiveAwayCommand;
import org.contrum.prisma.glowing.GlowingService;
import org.contrum.prisma.grant.Grant;
import org.contrum.prisma.holograms.HologramsService;
import org.contrum.prisma.inventorycache.InventoryCacheService;
import org.contrum.prisma.keyall.KeyAllService;
import org.contrum.prisma.kit.Kit;
import org.contrum.prisma.kit.KitManager;
import org.contrum.prisma.kit.commands.KitCommand;
import org.contrum.prisma.kit.commands.KitsCommand;
import org.contrum.prisma.koth.AbstractKoth;
import org.contrum.prisma.koth.KothService;
import org.contrum.prisma.koth.impl.PermanentKoth;
import org.contrum.prisma.koth.impl.ScheduledKoth;
import org.contrum.prisma.listener.KeysListener;
import org.contrum.prisma.listener.PlayerListener;
import org.contrum.prisma.listener.PunishmentListener;
import org.contrum.prisma.listener.ServerListener;
import org.contrum.prisma.lootbox.LootBoxService;
import org.contrum.prisma.lootbox.boxes.LootBoxes;
import org.contrum.prisma.metadatapurger.MetadataPurgerService;
import org.contrum.prisma.mongo.MongoBackend;
import org.contrum.prisma.motd.MOTD;
import org.contrum.prisma.motd.PaperMOTDService;
import org.contrum.prisma.npc.NPCAdapterService;
import org.contrum.prisma.outfits.OutfitsService;
import org.contrum.prisma.personalpermissions.PersonalPermissionService;
import org.contrum.prisma.placeholders.*;
import org.contrum.prisma.placeholders.papi.PrismaPlaceholderExpansion;
import org.contrum.prisma.profile.PaperProfileService;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.profile.chat.ChatType;
import org.contrum.prisma.profile.listener.ProfileListener;
import org.contrum.prisma.profile.listener.ProfilePackettListener;
import org.contrum.prisma.profile.log.ProfileLogService;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.punishment.Punishment;
import org.contrum.prisma.punishment.PunishmentService;
import org.contrum.prisma.queue.PaperQueueService;
import org.contrum.prisma.queue.Queue;
import org.contrum.prisma.queue.QueueService;
import org.contrum.prisma.rank.Rank;
import org.contrum.prisma.rank.RankService;
import org.contrum.prisma.reboot.PaperRebootService;
import org.contrum.prisma.reboot.command.RebootCommand;
import org.contrum.prisma.reclaim.ReclaimService;
import org.contrum.prisma.redeem.RedeemService;
import org.contrum.prisma.redeem.commands.RedeemCommand;
import org.contrum.prisma.redis.RedisBackend;
import org.contrum.prisma.runnable.PlayerRunnable;
import org.contrum.prisma.scoreboard.ScoreboardService;
import org.contrum.prisma.security.SecurityService;
import org.contrum.prisma.security.twofactorauth.TwoFactorAuthManager;
import org.contrum.prisma.server.PaperServersService;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.server.ServerSyncRunnable;
import org.contrum.prisma.server.ServersService;
import org.contrum.prisma.servermanager.ServerManagerListener;
import org.contrum.prisma.service.ServiceManager;
import org.contrum.prisma.settings.SettingsService;
import org.contrum.prisma.settings.listener.ProfileSettingsListener;
import org.contrum.prisma.spark.SparkService;
import org.contrum.prisma.spawn.SpawnService;
import org.contrum.prisma.spawn.commands.ListSpawnCommand;
import org.contrum.prisma.spawn.commands.RemoveSpawnCommannd;
import org.contrum.prisma.spawn.commands.SetSpawnCommand;
import org.contrum.prisma.spawn.commands.SpawnCommand;
import org.contrum.prisma.staff.evidences.StaffEvidence;
import org.contrum.prisma.staff.evidences.StaffEvidenceService;
import org.contrum.prisma.staff.evidences.redis.listener.StaffPunishmentsRedisListener;
import org.contrum.prisma.staff.staffmode.StaffModeListener;
import org.contrum.prisma.staff.staffmode.StaffModeService;
import org.contrum.prisma.staff.staffmode.freeze.task.FreezeTask;
import org.contrum.prisma.statistics.StatisticsService;
import org.contrum.prisma.sync.SyncService;
import org.contrum.prisma.systems.System;
import org.contrum.prisma.systems.SystemService;
import org.contrum.prisma.systems.impl.battlepass.BattlePassSystem;
import org.contrum.prisma.systems.impl.battlepass.metadata.ProfileBattlePassMetadata;
import org.contrum.prisma.systems.impl.battlepass.quests.BattlePassQuest;
import org.contrum.prisma.systems.impl.regionevents.Region;
import org.contrum.prisma.systems.impl.regionevents.RegionEventsSystem;
import org.contrum.prisma.tag.Tag;
import org.contrum.prisma.tag.TagSection;
import org.contrum.prisma.tag.TagService;
import org.contrum.prisma.tag.listener.TagListener;
import org.contrum.prisma.timedrewards.TimedRewardsService;
import org.contrum.prisma.timer.TimerPaperService;
import org.contrum.prisma.timer.impl.Timer;
import org.contrum.prisma.tips.TipsTask;
import org.contrum.prisma.trading.TradeService;
import org.contrum.prisma.treasurechest.TreasureChestService;
import org.contrum.prisma.tutorial.TutorialService;
import org.contrum.prisma.utils.ServerContext;
import org.contrum.prisma.utils.config.ConfigFile;
import org.contrum.prisma.utils.gson.*;
import org.contrum.prisma.utils.tick.TickManager;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.prisma.utils.timer.runnable.PlayerTimerRunnable;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.prisma.uuid.BukkitNameRetrievalService;
import org.contrum.prisma.uuid.NamesService;
import org.contrum.prisma.warp.WarpManager;
import org.contrum.prisma.warp.commands.WarpCommand;
import org.contrum.prisma.warp.commands.WarpsCommand;
import org.contrum.prisma.watch.PaperPrismaWatchService;
import org.contrum.prisma.watch.PrismaWatchService;
import org.contrum.prisma.watch.antidupe.WatchAntiDupeService;
import org.contrum.tritosa.LanguageHandler;
import org.contrum.tritosa.LanguageOptions;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;
import org.contrum.tritosa.placeholder.Placeholder;

import java.io.IOException;
import java.lang.reflect.Modifier;
import java.nio.file.Path;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Getter
public class PaperServices implements ServiceManager {

    private final PrismaCore plugin;

    private ConfigFile mainConfig;

    private final MenuManager menuManager;
    private final MongoBackend mongoBackend;
    private final RedisBackend redisBackend;

    private final PaperPrismaWatchService watchService;

    private final TickManager tickManager;

    public static final Gson GSON = new GsonBuilder()
            .excludeFieldsWithModifiers(Modifier.TRANSIENT, Modifier.STATIC)
            .registerTypeHierarchyAdapter(ItemStack.class, new OldItemStackSerializer())
            .registerTypeAdapter(LocalDateTime.class, new LocalDateTypeAdapter())
            .registerTypeAdapter(SellerItem.class, new SellerItemAdapter())
            .registerTypeAdapter(CuboidRegion.class, new CuboidRegionAdapter())
            .registerTypeAdapter(Instant.class, new InstantTypeAdapter())
            .registerTypeHierarchyAdapter(Location.class, new LocationAdapter())
            .registerTypeHierarchyAdapter(Vector.class, new VectorAdapter())
            .enableComplexMapKeySerialization()
            .serializeNulls()
            .create();

    private final NamesService namesService;

    private final ServersService serversService;

    private final RankService rankService;
    private final ProfileService profileService;
    private final ProfileLogService profileLogService;
    private final AddonService addonService;
    private final StaffModeService staffModeService;
    private final StaffEvidenceService staffEvidenceService;

    private final LanguageHandler languageHandler;
    private final Translator translator;
    private final PaperCommandManager commandManager;
    private final CacheService cacheService;
    private final NPCAdapterService npcAdapterService;
    private final PaperMOTDService motdService;
    private final ActionBarService actionBarService;
    private final CustomItemsService customItemsService;
    private final PunishmentService punishmentService;
    private final EconomyService economyService;
    private final PersonalPermissionService personalPermissionService;
    private final SpawnService spawnService;
    private final ChatService chatService;
    private final PaperQueueService queueService;
    private final CoinDropService coinDropService;
    private final BlockDropsService blockDropsService;
    private final DailyStreakManager dailyStreakManager;
    private final CustomDropService customDropService;
    private final CombatLoggerService combatLoggerService;
    private final ReclaimService reclaimService;
    private final TreasureChestService treasureChestService;
    private final KothService kothService;
    private SparkService sparkService;
    private final CustomArmorService customArmorService;
    private final CustomNPCService customNPCService;
    private final HologramsService hologramsService;
    private final GlowingService glowingService;
    private final TutorialService tutorialService;
    private final ClientsService clientsService;
    private final SecurityService securityService;
    private final DeathMessageService deathMessageService;
    private final GameEventsService gameEventsService;
    private final DropMultiplierService dropMultiplierService;
    private final MetadataPurgerService metadataPurgerService;
    private final DisguiseService disguiseService;
    private final EnderChestService enderChestService;
    private final EssenceTraderService essenceTraderService;
    private final CoinFlipService coinFlipService;
    private final StatisticsService statisticsService;
    private final SettingsService settingsService;
    //private final SpecialEventService specialEventService;
    private final SystemService systemService;
    private final TimerPaperService timerService;
    private final InventoryCacheService inventoryCacheService;
    private final OutfitsService outfitsService;
    private final LootBoxService lootBoxService;
    private final TimedRewardsService timedRewardsService;
    private final KeyAllService keyAllService;
    private final TagService tagService;
    private final TradeService tradeService;

    private ScoreboardService scoreboardService;

    private PrismaPlaceholderExpansion prismaPlaceholderExpansion;

    private final GiveAwayHandler giveAwayHandler;
    private final KitManager kitManager;
    private final PaperRebootService rebootService;
    private final RedeemService redeemService;
    //private final VoucherManager voucherManager;
    private final WarpManager warpManager;

    private final SyncService syncService;

    private RealMinesAPI realMines;
    private final PacketEventsAPI<?> packetEvents;
    private final Clans UClans;

    private final ScheduledExecutorService prismaAsyncExecutor = Executors.newScheduledThreadPool(4);
    private final ScheduledExecutorService databaseExecutor = Executors.newScheduledThreadPool(4); //Maybe we need more threads

    public PaperServices(PrismaCore plugin) {
        this.plugin = plugin;

        mainConfig = new ConfigFile(plugin, "config.yml");
        mainConfig.register(PrismaCoreSetting.class, null);
        FileConfiguration config = mainConfig.getConfig();

        this.menuManager = new MenuManager(plugin, new org.contrum.library.gson.Gson());

        mongoBackend = new MongoBackend(
                databaseExecutor,
                config.getString("connection.mongo.address"),
                config.getString("connection.mongo.username"),
                config.getString("connection.mongo.password"),
                config.getString("connection.mongo.authDatabase"),
                config.getString("connection.mongo.database"),
                config.getInt("connection.mongo.port"),
                config.getBoolean("connection.mongo.authenticate")
        );

        redisBackend = new RedisBackend(
                databaseExecutor,
                config.getString("connection.redis.address"),
                config.getString("connection.redis.username"),
                config.getString("connection.redis.password"),
                config.getInt("connection.redis.port"),
                config.getInt("connection.redis.databaseId"),
                config.getBoolean("connection.redis.useUsername"),
                config.getBoolean("connection.redis.authenticate"),
                GSON
        );

        packetEvents = PacketEvents.getAPI();

        try {
            realMines = RealMinesAPI.getInstance();
        } catch (NoClassDefFoundError ignore) {}

        UClans = (Clans) Bukkit.getPluginManager().getPlugin("UltimateClans");

        languageHandler = new LanguageHandler(plugin, LanguageOptions.builder()
                .redis(true)
                .redisCredentials(redisBackend)
                .mongoCredentials(mongoBackend));
        try {
            languageHandler.load("English", plugin.getDataFolder().toPath().resolve("lang_en.yml"));
        } catch (IOException e) {
            e.printStackTrace();
        }

        translator = languageHandler.getTranslator();
        this.registerPlaceholders();

        serversService = new PaperServersService(
                new PrismaServer(config.getString("server.data.name"),
                        config.getString("server.data.old_database_name"),
                        PrismaServer.ServerType.parse(config.getString("server.data.type"))),
                this);

        watchService = new PaperPrismaWatchService(this);

        tickManager = new TickManager(this);
        commandManager = new PaperCommandManager(this.plugin);

        addonService = new AddonService(this);
        rankService = new RankService(mongoBackend, redisBackend);
        profileService = new PaperProfileService(this);

        staffModeService = new StaffModeService(this);
        staffEvidenceService = new StaffEvidenceService(this);

        if (PrismaCoreSetting.SCOREBOARD_ENABLED) {
            scoreboardService = new ScoreboardService(this);
        }

        namesService = new NamesService(redisBackend);
        namesService.addServiceRetriever(new BukkitNameRetrievalService());

        chatService = new ChatService(this);
        punishmentService = new PunishmentService(mongoBackend, redisBackend);
        personalPermissionService = new PersonalPermissionService(this);
        actionBarService = new ActionBarService(this);
        customItemsService = new CustomItemsService(this);
        customItemsService.loadItems();

        profileLogService = new ProfileLogService(this);
        motdService = new PaperMOTDService(this);

        queueService = new PaperQueueService(this);
        queueService.setQueuePriorities(PrismaCoreSetting.QUEUE_PRIORITIES);

        cacheService = new CacheService(this);
        npcAdapterService = new NPCAdapterService(this);
        tagService = new TagService(this);
        coinDropService = new CoinDropService(this);
        blockDropsService = new BlockDropsService(this);
        customDropService = new CustomDropService(this);
        combatLoggerService = new CombatLoggerService(this);
        try {
             sparkService = new SparkService(this);
        } catch (Error | Exception exception) {
            sparkService = null;
            Bukkit.getLogger().warning("Unable to load spark service!");
            exception.printStackTrace();
        }
        kothService = new KothService(this);
        customArmorService = new CustomArmorService(this);
        customNPCService = new CustomNPCService(this);
        hologramsService = new HologramsService(this);
        glowingService = new GlowingService(this);
        tutorialService = new TutorialService(this);
        clientsService = new ClientsService(this);
        securityService = new SecurityService(this);
        gameEventsService = new GameEventsService(this);
        disguiseService = new DisguiseService(this);
        essenceTraderService = new EssenceTraderService(this);
        enderChestService = new EnderChestService(this);
        dropMultiplierService = new DropMultiplierService(this);
        metadataPurgerService = new MetadataPurgerService(this);
        //specialEventService = new SpecialEventService(this);
        treasureChestService = new TreasureChestService(this);
        reclaimService = new ReclaimService(this);
        systemService = new SystemService(this);
        rebootService = new PaperRebootService(this);
        spawnService = new SpawnService(this);
        deathMessageService = new DeathMessageService(this);

        economyService = new EconomyService(this);
        dailyStreakManager = new DailyStreakManager(this);
        giveAwayHandler = new GiveAwayHandler(this);
        kitManager = new KitManager(this);
        redeemService = new RedeemService(this);
        statisticsService = new StatisticsService(this);
        settingsService = new SettingsService(this);
        inventoryCacheService = new InventoryCacheService(this);
        outfitsService = new OutfitsService(this);
        //voucherManager = new VoucherManager(this);
        warpManager = new WarpManager(this);
        timerService = new TimerPaperService(this);
        coinFlipService = new CoinFlipService(this);
        lootBoxService = new LootBoxService(this);
        timedRewardsService = new TimedRewardsService(this);
        syncService = new SyncService(this);
        tradeService = new TradeService(this);
        keyAllService = new KeyAllService(this);

        this.registerCommands();

        registerListeners();
        registerTasks();
        registerActionBars();
    }

    private void registerPlaceholders() {
        languageHandler.registerPlaceholder(Player.class, new PlayerPlaceholder(this));
        languageHandler.registerPlaceholder(Profile.class, new ProfilePlaceholder(this));
        languageHandler.registerPlaceholder(Rank.class, new RankPlaceholder());
        languageHandler.registerPlaceholder(Grant.class, new GrantPlaceholder(this));
        languageHandler.registerPlaceholder(CustomArmor.class, new CustomArmorPlaceholder(this));
        languageHandler.registerPlaceholder(Punishment.class, new PunishmentPlaceholder());
        languageHandler.registerPlaceholder(SimpleUser.class, new SimpleUserPlaceholder());
        languageHandler.registerPlaceholder(StaffEvidence.class, new StaffPunishmentPlaceholders(this));
        languageHandler.registerPlaceholder(AbstractKoth.class, new KothPlaceholder());
        languageHandler.registerPlaceholder(PrismaServer.class, new PrismaServerPlaceholder(translator));
        languageHandler.registerPlaceholder(Queue.class, new QueuePlaceholder());
        languageHandler.registerPlaceholder(Duration.class, new DurationPlaceholders());
        languageHandler.registerPlaceholder(Event.class, new GameEventPlaceholder(this));
        languageHandler.registerPlaceholder(Tag.class, new TagPlaceholder());
        languageHandler.getPlaceholderManager().register(new PrismaFontPlaceHolder());
        languageHandler.getPlaceholderManager().register(new PrismaGlobalPlaceholder());

        if(Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            prismaPlaceholderExpansion = new PrismaPlaceholderExpansion(this);

            prismaPlaceholderExpansion.register();
        }
    }

    private void registerListeners() {
        PluginManager pluginManager = Bukkit.getPluginManager();

        pluginManager.registerEvents(new ProfileListener(this), plugin);
        pluginManager.registerEvents(new ProfileSettingsListener(this), plugin);
        pluginManager.registerEvents(new StaffModeListener(this), plugin);
        pluginManager.registerEvents(new PlayerListener(this), plugin);
        pluginManager.registerEvents(new KeysListener(), plugin);

        ServerListener serverListener = new ServerListener(this);

        pluginManager.registerEvents(serverListener, plugin);
        redisBackend.registerListener(serverListener);

        redisBackend.registerListener(new PunishmentListener(this));
        redisBackend.registerListener(new StaffPunishmentsRedisListener(staffEvidenceService));
        redisBackend.registerListener(new ServerManagerListener(this, serversService));
        redisBackend.registerListener(new ProfilePackettListener(this));
        redisBackend.registerListener(new TagListener(tagService));
    }

    private void registerCommands() {

        commandManager.registerDependency(Translator.class, translator);
        commandManager.registerDependency(PaperServices.class, this);
        commandManager.registerDependency(ServiceManager.class, this);

        commandManager.registerDependency(RedisBackend.class, redisBackend);
        commandManager.registerDependency(MongoBackend.class, mongoBackend);

        commandManager.registerDependency(ProfileService.class, profileService);
        commandManager.registerDependency(PaperPrismaWatchService.class, watchService);
        commandManager.registerDependency(WatchAntiDupeService.class, watchService.getAntiDupeService());
        commandManager.registerDependency(RankService.class, rankService);
        commandManager.registerDependency(StaffModeService.class, staffModeService);
        commandManager.registerDependency(ServersService.class, serversService);
        commandManager.registerDependency(PunishmentService.class, punishmentService);
        commandManager.registerDependency(StaffEvidenceService.class, staffEvidenceService);
        commandManager.registerDependency(QueueService.class, queueService);
        commandManager.registerDependency(CoinDropService.class, coinDropService);
        commandManager.registerDependency(CustomDropService.class, customDropService);
        commandManager.registerDependency(BlockDropsService.class, blockDropsService);
        commandManager.registerDependency(CombatLoggerService.class, combatLoggerService);
        commandManager.registerDependency(TagService.class, tagService);
        commandManager.registerDependency(CustomArmorService.class, customArmorService);
        commandManager.registerDependency(CustomNPCService.class, customNPCService);
        commandManager.registerDependency(SpawnService.class, spawnService);
        commandManager.registerDependency(KitManager.class, kitManager);
        commandManager.registerDependency(PaperMOTDService.class, motdService);
        commandManager.registerDependency(SecurityService.class, securityService);
        commandManager.registerDependency(TwoFactorAuthManager.class, securityService.getTwoFactorAuthManager());
        commandManager.registerDependency(KothService.class, kothService);
        commandManager.registerDependency(TreasureChestService.class, treasureChestService);
        commandManager.registerDependency(DisguiseService.class, disguiseService);
        commandManager.registerDependency(CustomItemsService.class, customItemsService);
        commandManager.registerDependency(DropMultiplierService.class, dropMultiplierService);
        commandManager.registerDependency(PersonalPermissionService.class, personalPermissionService);
        commandManager.registerDependency(EnderChestService.class, enderChestService);
        commandManager.registerDependency(CoinFlipService.class, coinFlipService);
        commandManager.registerDependency(ProfileLogService.class, profileLogService);
        commandManager.registerDependency(InventoryCacheService.class, inventoryCacheService);
        commandManager.registerDependency(EconomyService.class, economyService);
        commandManager.registerDependency(OutfitsService.class, outfitsService);
        commandManager.registerDependency(LootBoxService.class, lootBoxService);
        commandManager.registerDependency(KeyAllService.class, keyAllService);
        //commandManager.registerDependency(ScoreboardService.class, scoreboardService);
        commandManager.registerDependency(TimerPaperService.class, timerService);
        //commandManager.registerDependency(SpecialEventService.class, specialEventService);
        commandManager.registerDependency(SparkService.class, sparkService);
        commandManager.registerDependency(TradeService.class, tradeService);
        for (System system : this.getSystemService().getSystems()) {
            commandManager.registerDependency(system.getClass(), system);
        }

        commandManager.getCommandContexts().registerContext(Profile.class, c -> {
            String name = c.popFirstArg();
            UUID uniqueId = namesService.getUniqueId(name);

            Profile profile = profileService.getProfile(uniqueId);

            if (profile == null) {
                translator.send(c.getSender(), "ERRORS.USER_NOT_FOUND");
                throw new InvalidCommandArgument(false);
            }

            return profile;
        });

        commandManager.getCommandContexts().registerIssuerAwareContext(ServerContext.class, c -> {
            String name = c.getFirstArg();
            if (name == null) {
                if (!c.isOptional()) {
                    throw new InvalidCommandArgument(false);
                }
                else {
                    return null;
                }
            }

            ServerContext context = ServerContext.build(serversService, name);
            if (context == null) {
                if (!c.isOptional()) {
                    throw new InvalidCommandArgument(false);
                }
                else {
                    return null;
                }
            }

            c.popFirstArg();
            return context;
        });

        commandManager.getCommandContexts().registerContext(Profile.class, c -> {
            String name = c.popFirstArg();

            Profile profile = profileService.getProfile(name);

            if (profile == null) {
                translator.send(c.getSender(), "ERRORS.USER_NOT_FOUND");
                throw new InvalidCommandArgument(false);
            }

            return profile;
        });

        commandManager.getCommandContexts().registerContext(CustomArmor.class, c -> {
            String name = c.popFirstArg();

            CustomArmor armor = customArmorService.getArmor(name);
            if (armor == null) {
                translator.send(c.getSender(), "CUSTOM_ARMOR.DOESNT_EXIST");
                throw new InvalidCommandArgument(false);
            }

            return armor;
        });

        commandManager.getCommandContexts().registerContext(PrismaServer.class, c -> {
            String name = c.popFirstArg();

            PrismaServer server = serversService.getServer(name);

            if (server == null) {
                translator.send(c.getSender(), "ERRORS.SERVER_DOES_NOT_EXIST");
                throw new InvalidCommandArgument(false);
            }

            return server;
        });

        commandManager.getCommandContexts().registerContext(Tag.class, c -> {
            String name = c.popFirstArg();

            Tag tag = tagService.getTag(name);

            if (tag == null) {
                translator.send(c.getSender(), "TAGS.DOESNT_EXIST");
                throw new InvalidCommandArgument(false);
            }

            return tag;
        });
        commandManager.getCommandContexts().registerContext(TagSection.class, c -> {
            String name = c.popFirstArg();

            TagSection tagSection = tagService.getTagSection(name);

            if (tagSection == null) {
                translator.send(c.getSender(), "TAGS.SECTION_DOESNT_EXIST");
                throw new InvalidCommandArgument(false);
            }

            return tagSection;
        });

        commandManager.getCommandCompletions().registerStaticCompletion("armorConfiguration", List.of("PRIORITY", "PROTECTION", "DURABILITY", "SHARPNESS", "EFFICIENCY", "HEALTH", "DAMAGE", "SPEED", "ATTACK_SPEED", "COLOR", "DISPLAY_NAME", "SWORD_ITEM", "SHULKER_MATERIAL", "COMPRESS_OWNER", "COMPRESS_REQUIRED", "COMPRESS_COST", "MATERIAL", "BLOCK_MATERIAL", "UNBREAKABLE", "SKULL_SKIN", "PICKAXE_MULTIPLY_CHANCE"));
        commandManager.getCommandCompletions().registerStaticCompletion("materials", Arrays.stream(Material.values()).map(Material::name).toList());
        commandManager.getCommandCompletions().registerCompletion("chat_types", c -> Arrays.stream(ChatType.values()).map(ChatType::name).toList());
        commandManager.getCommandCompletions().registerCompletion("customitems", c -> customItemsService.getItems().keySet());
        commandManager.getCommandCompletions().registerAsyncCompletion("motdname", c -> motdService.getMOTDs().stream().map(MOTD::getName).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("ranks", c -> rankService.getRanks().stream().map(Rank::getName).collect(Collectors.toList()));
        commandManager.getCommandCompletions().registerAsyncCompletion("onlineplayers", c -> profileService.getOnlineProfiles().values().stream().map(Profile::getName).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("kits",c -> kitManager.getKits().stream().map(Kit::getName).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("customArmors", c -> customArmorService.getCustomArmors().keySet());
        commandManager.getCommandCompletions().registerAsyncCompletion("koths", c -> kothService.getKothList().keySet());
        commandManager.getCommandCompletions().registerAsyncCompletion("gameevents", c -> Arrays.stream(Events.values()).map(Events::name).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("permanentkoths", c -> kothService.getPermanentKoths().stream().map(AbstractKoth::getName).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("scheduledkoths", c -> kothService.getScheduledKoths().stream().map(AbstractKoth::getName).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("onlineplayersbutself", c -> profileService.getOnlineProfiles().values().stream().filter(profile -> !c.getPlayer().getUniqueId().equals(profile.getUniqueId())).map(Profile::getName).collect(Collectors.toList()));
        //commandManager.getCommandCompletions().registerAsyncCompletion("specialevents",c -> specialEventService.getEvents().keySet());
        commandManager.getCommandCompletions().registerAsyncCompletion("worlds", c -> Bukkit.getWorlds().stream().map(World::getName).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("servers", c -> serversService.getServers().stream().map(PrismaServer::getName).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("boards", c -> scoreboardService.getPresets().keySet());
        commandManager.getCommandCompletions().registerAsyncCompletion("timers", c -> timerService.getTimers().stream().map(Timer::getName).collect(Collectors.toSet()));
        commandManager.getCommandCompletions().registerAsyncCompletion("metadatas", c -> List.of(ProfilePaperMetadata.class.getName(), ProfileBattlePassMetadata.class.getName()));
        commandManager.getCommandCompletions().registerAsyncCompletion("queues", c -> queueService.getQueues().stream().map(Queue::getServer).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("battlepass_quests", c -> systemService.getSystem(BattlePassSystem.class).getQuestManager().getQuests().stream().map(BattlePassQuest::getFullID).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("region_events", c -> systemService.getSystem(RegionEventsSystem.class).getEventsClasses().keySet());
        commandManager.getCommandCompletions().registerAsyncCompletion("regions", c -> systemService.getSystem(RegionEventsSystem.class).getRegions().keySet());
        commandManager.getCommandCompletions().registerAsyncCompletion("tags", c -> tagService.getTags().keySet());
        commandManager.getCommandCompletions().registerAsyncCompletion("tagsections", c -> tagService.getTagSections().keySet());
        commandManager.getCommandCompletions().registerAsyncCompletion("lootboxes", c -> Arrays.stream(LootBoxes.values()).map(Enum::name).toList());
        commandManager.getCommandCompletions().registerAsyncCompletion("playercooldowns", c -> {
            UUID uuid = c.getContextValue(UUID.class);
            Profile profile = profileService.getOrLoadProfileAsync(uuid).join();
            return profile.getServerMetadata(profileService, ProfilePaperMetadata.class).getCooldows().keySet();
        });
        commandManager.getCommandCompletions().registerCompletion("players", c -> profileService.getOnlineProfiles().values().stream().map(Profile::getName).toList());

        commandManager.getCommandContexts().registerContext(UUID.class, c -> {
            String input = c.popFirstArg();

            //Find disguised players
            for (Profile disguisedPlayer : this.getDisguiseService().getDisguisedPlayers()) {
                if (disguisedPlayer.getName().equalsIgnoreCase(input)) {
                    return disguisedPlayer.getUniqueId();
                }
            }

            //Disguise check
            UUID uuid = input == null ? null : namesService.getUniqueId(input);
            if (uuid != null) {
                CommandSender sender = c.getSender();
                if (sender instanceof Player player && player.hasPermission("core.staff")) { //Staff bypass disguise
                    return uuid;
                }

                return disguiseService.isDisguised(uuid) ? null : uuid; //Null if player is disguised.
            }
            return null;
        });

        commandManager.getCommandContexts().registerContext(Kit.class, c -> {
            String input = c.popFirstArg();

            Kit kit = kitManager.getKit(input);
            if (kit == null) {
                translator.send(c.getSender(), "COMMANDS.KIT.DOESNT_EXIST");
                throw new InvalidCommandArgument(false);
            }

            return kit;
        });

        commandManager.getCommandContexts().registerContext(AbstractKoth.class, c -> {
            String input = c.popFirstArg();

            AbstractKoth koth = kothService.getKoth(input);
            if (koth == null) {
                translator.send(c.getSender(), "COMMANDS.KOTH.NOT_FOUND");
                throw new InvalidCommandArgument(false);
            }

            return koth;
        });

        commandManager.getCommandContexts().registerContext(ScheduledKoth.class, c -> {
            String input = c.popFirstArg();

            ScheduledKoth koth = kothService.getScheduledKoth(input);
            if (koth == null) {
                translator.send(c.getSender(), "COMMANDS.KOTH.NOT_FOUND");
                throw new InvalidCommandArgument(false);
            }

            return koth;
        });

        commandManager.getCommandContexts().registerContext(PermanentKoth.class, c -> {
            String input = c.popFirstArg();

            PermanentKoth koth = kothService.getPermanentKoth(input);
            if (koth == null) {
                translator.send(c.getSender(), "COMMANDS.KOTH.NOT_FOUND");
                throw new InvalidCommandArgument(false);
            }

            return koth;
        });

        commandManager.getCommandContexts().registerContext(Region.class, c -> {
            String input = c.popFirstArg();

            Region region = systemService.getSystem(RegionEventsSystem.class).getRegion(input);
            if (region == null) {
                translator.send(c.getSender(), "REGION_EVENTS.NOT_FOUND");
                throw new InvalidCommandArgument(false);
            }

            return region;
        });

        commandManager.getCommandContexts().registerIssuerAwareContext(Duration.class, c -> {
            String raw = c.hasFlag("disablePop") ? c.getFirstArg() : c.popFirstArg();
            if (raw == null) {
                if (!c.isOptional()) {
                    translator.send(c.getSender(), "ERRORS.INVALID_DURATION");
                    throw new InvalidCommandArgument(false);
                }

                return Duration.ofSeconds(-1);
            }

            if (raw.equalsIgnoreCase("permanent") || raw.equalsIgnoreCase("permament") || raw.equalsIgnoreCase("perm")) {
                if (c.hasFlag("disablePop")) c.popFirstArg();
                return Duration.ofSeconds(-1);
            }
            Duration duration = TimeUtils.parseDuration(raw);
            if (duration == null) {
                if (!c.isOptional()) {
                    translator.send(c.getSender(), "ERRORS.INVALID_DURATION");
                    throw new InvalidCommandArgument(false);
                }

                return duration;
            }

            if (c.hasFlag("disablePop")) c.popFirstArg();
            return duration;
        });

        commandManager.registerCommand(new PrismaCoreCommand());

        commandManager.registerCommand(new RankCommands());

        commandManager.registerCommand(new GrantCommand());
        commandManager.registerCommand(new GrantsCommand());

        commandManager.registerCommand(new PersonalPermissionsCommand());

        commandManager.registerCommand(new PlayerAttributesCommand());
        commandManager.registerCommand(new CustomDropCommand());

        commandManager.registerCommand(new GamemodeCommand());
        commandManager.registerCommand(new CreativeCommand());
        commandManager.registerCommand(new SpectatorCommand());
        commandManager.registerCommand(new SurvivalCommand());

        commandManager.registerCommand(new BCRawCommand());
        commandManager.registerCommand(new IpLookup());

        commandManager.registerCommand(new ItemSearchCommand());

        commandManager.registerCommand(new FlyCommand());
        commandManager.registerCommand(new HealCommand());
        commandManager.registerCommand(new KeyCommand());
        commandManager.registerCommand(new FeedCommand());
        commandManager.registerCommand(new ClearCommand());
        commandManager.registerCommand(new ChunksCommand());

        commandManager.registerCommand(new PunishmentLogCommand());
        commandManager.registerCommand(new PendingEvidencesCommand());
        commandManager.registerCommand(new EvidencesCommand());

        commandManager.registerCommand(new TeleportAllCommand());
        commandManager.registerCommand(new TeleportExactCommand());
        commandManager.registerCommand(new TeleportHereCommand());
        commandManager.registerCommand(new TeleportTopCommand());
        commandManager.registerCommand(new TeleportToCommand());
        commandManager.registerCommand(new TeleportWorldCommand());
        commandManager.registerCommand(new BackCommand());

        commandManager.registerCommand(new CheckCommand());

        commandManager.registerCommand(new BanCommand());
        commandManager.registerCommand(new IpBanCommand());
        commandManager.registerCommand(new BlackListCommand());
        commandManager.registerCommand(new MuteCommand());
        commandManager.registerCommand(new KickCommand());
        commandManager.registerCommand(new WarnCommand());

        commandManager.registerCommand(new UnbanCommand());
        commandManager.registerCommand(new UnBlackListCommand());
        commandManager.registerCommand(new UnMuteCommand());

        commandManager.registerCommand(new ServerManagerCommand());

        commandManager.registerCommand(new DevCommand());

        commandManager.registerCommand(new EventsCommand());
        commandManager.registerCommand(new EconomyCommands());

        commandManager.registerCommand(new RewardsCommand());

        commandManager.registerCommand(new DropMultiplierCommand());

        commandManager.registerCommand(new GiveAwayCommand());

        commandManager.registerCommand(new KitsCommand());
        commandManager.registerCommand(new KitCommand());

        commandManager.registerCommand(new RallyCommand());

        commandManager.registerCommand(new CoinDropsCommand());
        commandManager.registerCommand(new BlockDropCommand());
        commandManager.registerCommand(new CustomArmorCommand());
        commandManager.registerCommand(new CustomItemsCommand());
        commandManager.registerCommand(new LegendaryPickaxeCommand());
        commandManager.registerCommand(new CustomNPCCommand());
        commandManager.registerCommand(new TreasureChestCommand());

        commandManager.registerCommand(new AddonsCommand());

        commandManager.registerCommand(new RebootCommand());
        commandManager.registerCommand(new SparkCommand());

        commandManager.registerCommand(new RedeemCommand(redeemService));

        //commandManager.registerCommand(new VoucherCommand(voucherManager));

        commandManager.registerCommand(new WarpCommand(warpManager));
        commandManager.registerCommand(new WarpsCommand(warpManager));

        commandManager.registerCommand(new QueueCommands());
        commandManager.registerCommand(new ReclaimCommand());
        commandManager.registerCommand(new SettingsCommand());
        commandManager.registerCommand(new VirtualCommand());
        commandManager.registerCommand(new PayCommand());
        commandManager.registerCommand(new BalanceCommand());
        commandManager.registerCommand(new BalTopCommand());

        commandManager.registerCommand(new MinesCommand());
        commandManager.registerCommand(new EnderchestCommand());
        commandManager.registerCommand(new ListCommand());
        commandManager.registerCommand(new StatsCommand());
        commandManager.registerCommand(new TutorialCommand());
        commandManager.registerCommand(new VoteCommand());
        commandManager.registerCommand(new HelpopCommand());
        commandManager.registerCommand(new ReportCommand());
        commandManager.registerCommand(new ChatColorCommand());
        commandManager.registerCommand(new AbilitiesCommand());

        commandManager.registerCommand(new DiscordCommand());
        commandManager.registerCommand(new InstagramCommand());
        commandManager.registerCommand(new SocialsCommand());
        commandManager.registerCommand(new TeamSpeakCommand());
        commandManager.registerCommand(new TelegramCommand());
        commandManager.registerCommand(new TwitterCommand());
        commandManager.registerCommand(new WebsiteCommand());

        commandManager.registerCommand(new IgnoreCommand());
        commandManager.registerCommand(new PrivateMessageCommand());
        commandManager.registerCommand(new ReplyCommand());
        commandManager.registerCommand(new TogglePrivateMessagesCommand());

        commandManager.registerCommand(new NewVideoCommand());
        commandManager.registerCommand(new StreamCommand());

        commandManager.registerCommand(new ProfileCommand());
        commandManager.registerCommand(new StaffModeCommand());
        commandManager.registerCommand(new TwoFactorsAuthCommand());
        commandManager.registerCommand(new FreezeCommand());
        commandManager.registerCommand(new InvseeCommand());
        commandManager.registerCommand(new NotesCommand());
        commandManager.registerCommand(new DeathsCommand());
        commandManager.registerCommand(new VanishCommand());
        commandManager.registerCommand(new DisguiseCommand());
        commandManager.registerCommand(new DisguiseLogsCommand());
        commandManager.registerCommand(new DisguisePlayersCommand());

        commandManager.registerCommand(new AltsCommand());
        commandManager.registerCommand(new AdminChatCommand());
        commandManager.registerCommand(new ChatCommand());
        commandManager.registerCommand(new ClearChatCommand());
        commandManager.registerCommand(new DeveloperChatCommand());
        commandManager.registerCommand(new MuteCommand());
        commandManager.registerCommand(new SlowChatCommand());
        commandManager.registerCommand(new StaffChatCommand());
        commandManager.registerCommand(new MuteChatCommand());

        commandManager.registerCommand(new KothBaseCommand());
        commandManager.registerCommand(new ScheduledKothCommand());
        commandManager.registerCommand(new PermanentKothCommand());

        commandManager.registerCommand(new SudoCommand());
        commandManager.registerCommand(new SudoAllCommand());
        commandManager.registerCommand(new KeysCommand());

        commandManager.registerCommand(new MOTDCommand());

        commandManager.registerCommand(new CraftCommand());
        commandManager.registerCommand(new GodModeCommand());
        commandManager.registerCommand(new SetSlotsCommand());
        commandManager.registerCommand(new SpeedCommand());
        commandManager.registerCommand(new UnBreakableCommand());
        commandManager.registerCommand(new MoreCommand());
        commandManager.registerCommand(new RepairCommand());
        commandManager.registerCommand(new RepairAllCommand());

        commandManager.registerCommand(new ListSpawnCommand());
        commandManager.registerCommand(new RemoveSpawnCommannd());
        commandManager.registerCommand(new SetSpawnCommand());
        commandManager.registerCommand(new SpawnCommand());
        commandManager.registerCommand(new GarbageCommand());

        commandManager.registerCommand(new TagCommand());
        commandManager.registerCommand(new TagSectionCommand());

        commandManager.registerCommand(new BattlePassManageCommand());
        commandManager.registerCommand(new BattlePassCommand());
        commandManager.registerCommand(new BattlePassQuestsCommand());

        commandManager.registerCommand(new NightMarketCommand());
        commandManager.registerCommand(new ScoreboardCommand());
        commandManager.registerCommand(new TimerCommand());
        commandManager.registerCommand(new NoTrackHideCommand());
        commandManager.registerCommand(new SOTWCommand());
        commandManager.registerCommand(new PlayerCooldownCommand());
        commandManager.registerCommand(new EssenceTraderCommand());
        commandManager.registerCommand(new AirDropCommand());
        commandManager.registerCommand(new ReviveCommand());
        commandManager.registerCommand(new ThankCommand());
        commandManager.registerCommand(new ReviveAllCommand());
        commandManager.registerCommand(new PvPTimerCommand());
        commandManager.registerCommand(new GGWaveCommand());
        commandManager.registerCommand(new DTCCommand());
        commandManager.registerCommand(new JoinMeCommand());
        commandManager.registerCommand(new CoinFlipCommand());
        commandManager.registerCommand(new InventoryCacheCommand());
        commandManager.registerCommand(new ChatTypeCommand());
        commandManager.registerCommand(new RocketSpeedCommand());
        commandManager.registerCommand(new RegionsCommand());
        commandManager.registerCommand(new WitchHouseCommand());
        commandManager.registerCommand(new MetadataPurgeCommand());
        commandManager.registerCommand(new OutfitCommand());
        commandManager.registerCommand(new FunnyItemsCommand());
        commandManager.registerCommand(new LootBoxCommand());
        commandManager.registerCommand(new AnniversaryCommand());
        commandManager.registerCommand(new AntiDupeCommand());
        commandManager.registerCommand(new KeyAllCommand());
        commandManager.registerCommand(new OrdenCommand());
        //commandManager.registerCommand(new SpecialEventCommand());
        //commandManager.registerCommand(new HideAndSeekEventCommand());

        commandManager.registerCommand(new SyncCommand());
        commandManager.registerCommand(new TimedRewardsCommand());
        commandManager.registerCommand(new SendPMToDiscordCommand());
        commandManager.registerCommand(new TradeCommand());
    }

    private void registerTasks() {
        prismaAsyncExecutor.scheduleAtFixedRate(
                new ServerSyncRunnable(this),
                5,
                5,
                TimeUnit.SECONDS
        );

        prismaAsyncExecutor.scheduleAtFixedRate(
                new FreezeTask(this.getStaffModeService().getFreezeManager()),
                5,
                1,
                TimeUnit.SECONDS
        );

        Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, new PlayerTimerRunnable(profileService), 20, 1);
        Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, new PlayerRunnable(this), 20, 5);
        new TipsTask(this);
    }

    private void registerActionBars() {
        this.actionBarService.registerGlobalActionBar(new GlobalPlaceHolderActionBar(translator, "CombatTag", 10, (p) -> {

            ProfilePaperMetadata metadata = profileService.getServerMetadata(p.getUniqueId(), ProfilePaperMetadata.class);
            return metadata.isTagged();
        }, "COMBAT_TAG.ACTIONBAR").setFunction((p) -> {
            ProfilePaperMetadata metadata = profileService.getServerMetadata(p.getUniqueId(), ProfilePaperMetadata.class);

            Duration cooldown = metadata.getTag();
            return new Object[]{LocalPlaceholders.builder()
                    .add("<duration_short>", cooldown == null ? "" : TimeUtils.getFormattedTime(cooldown.toMillis(), false))
                    .add("<duration_full>", cooldown == null ? "" : TimeUtils.getFormattedTime(cooldown.toMillis(), true))};
        }));

        this.actionBarService.registerGlobalActionBar(new GlobalPlaceHolderActionBar(translator, "Queue", 20, (p) -> {
            Queue queue = this.getQueueService().getQueueByPlayer(p.getUniqueId());
            return queue != null;
        }, "QUEUE.ACTIONBAR").setFunction((p) -> {
            Queue queue = this.getQueueService().getQueueByPlayer(p.getUniqueId());

            LocalPlaceholders placeholders = new LocalPlaceholders()
                    .add("<queue_position>", queue.getPosition(p.getUniqueId()) + "")
                    .add("<queue_size>", queue.getSize() + "");

            return new Object[]{placeholders, queue};
        }));

        this.actionBarService.registerGlobalActionBar(new GlobalPlaceHolderActionBar(translator, "DropMultiplier", 2, (p) -> {
            ProfilePaperMetadata metadata = profileService.getServerMetadata(p.getUniqueId(), ProfilePaperMetadata.class);
            return metadata.hasTimer(DropMultiplierTimer.class);
        }, "ACTIONBAR.DROP_MULTIPLIER_LEFT").setFunction((p) -> {
            ProfilePaperMetadata metadata = profileService.getServerMetadata(p.getUniqueId(), ProfilePaperMetadata.class);

            return new Object[]{Duration.ofMillis(metadata.getTimer(DropMultiplierTimer.class).getRemaining())};
        }));
    }

    public void reload() {
        languageHandler.reload();
        this.mainConfig = new ConfigFile(this.getPlugin(), "config.yml");
        mainConfig.register(PrismaCoreSetting.class, null);

        addonService.reload();

        treasureChestService.reload();
        customItemsService.reload();
        reclaimService.reload();
        systemService.reload();
        securityService.reload();
        essenceTraderService.reload();
        lootBoxService.reload();
        timedRewardsService.reload();
        queueService.setQueuePriorities(PrismaCoreSetting.QUEUE_PRIORITIES);
        if (scoreboardService != null) scoreboardService.reload();
    }

    public void shutdown() {
        customNPCService.shutdown();
        hologramsService.shutdown();
        redeemService.shutdown();
        coinDropService.shutdown();
        customDropService.shutdown();
        systemService.unload();
        serversService.shutdown();
        customArmorService.shutdown();
        clientsService.shutdown();
        kothService.shutdown();
        customItemsService.unload();
        treasureChestService.unload();
        essenceTraderService.unload();
        blockDropsService.unload();
        profileLogService.shutdown();
        disguiseService.shutdown();
        inventoryCacheService.shutdown();
        statisticsService.shutdown();
        coinFlipService.shutdown();
        settingsService.shutdown();
        watchService.shutdown();
        //specialEventService.destroy();
        spawnService.save();
        tagService.saveAll();

        profileService.shutdown();
        for (Player player : Bukkit.getOnlinePlayers()) {
            player.setMetadata("loggeout", new FixedMetadataValue(plugin, true));
        }

        for (World world : Bukkit.getWorlds()) {
            for (Entity entity : world.getEntities()) {
                if (entity.hasMetadata("CombatLogger")) {
                    entity.remove();
                }
            }
        }

        kitManager.save();
        warpManager.saveWarps();

        if(Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            prismaPlaceholderExpansion.unregister();
        }

        prismaAsyncExecutor.shutdown();
        databaseExecutor.shutdown();
    }

    @Override
    public AddonService getAddonService() {
        return addonService;
    }

    @Override
    public PaperPrismaWatchService getPrismaWatchService() {
        return watchService;
    }

    @Override
    public ScheduledExecutorService getExecutor() {
        return prismaAsyncExecutor;
    }

    @Override
    public Logger getLogger() {
        return Bukkit.getLogger();
    }

    public void registerCommand(BaseCommand baseCommand) {
        commandManager.registerCommand(baseCommand);
    }

    public void unregisterCommand(BaseCommand baseCommand) {
        commandManager.unregisterCommand(baseCommand);
    }

    public void registerCommandDependency(Class<?> clazz, Object instance) {
        commandManager.registerDependency(clazz, instance);
    }

    public <T> void registerPlaceholder(Class<T> clazz, Placeholder<T> placeholder) {
        languageHandler.registerPlaceholder(clazz, placeholder);
    }

    public void registerLanguageExtension(JavaPlugin plugin, String language, Path path) throws IOException {
        languageHandler.getLanguage(language).addExtension(plugin, path);
    }

    public boolean isUClansWorking(){
        return UClans != null && UClans.isEnabled();
    }
}